package com.renpho.erp.bpm.client.feign;

import com.renpho.erp.bpm.client.process.command.RemoteProcessInstanceStartCommand;
import com.renpho.erp.bpm.client.process.query.ProcessInstanceNodeLogQuery;
import com.renpho.erp.bpm.client.process.query.ProcessInstanceUserQuery;
import com.renpho.erp.bpm.client.process.vo.ProcessInstanceLogVO;
import com.renpho.erp.bpm.client.process.vo.ProcessInstanceRecordVO;
import com.renpho.erp.bpm.client.process.vo.ProcessInstanceUserVo;
import com.renpho.erp.openfeign.annotation.NoToken;
import com.renpho.karma.dto.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/31 14:53
 **/
@FeignClient("erp-bpm")
public interface RemoteProcessInstanceClient {

    /**
     * 开启流程实例接口
     *
     * @return com.renpho.karma.dto.R
     * <AUTHOR>
     * @Date 14:46 2025/1/6
     * @Param [command]
     **/
    @NoToken
    @PostMapping("/client/process/instance/start")
    R<String> startProcessInstance(@RequestBody RemoteProcessInstanceStartCommand command);

    /**
     * 查询流程实例接口
     *
     * @return com.renpho.karma.dto.R
     * <AUTHOR>
     * @Date 14:47 2025/1/6
     * @Param [processInstanceId]
     **/
    @NoToken
    @GetMapping("/client/process/instance/")
    R<ProcessInstanceRecordVO> getProcessInstance(@RequestParam String processInstanceId);

    /**
     * 查询流程实例当前审批人接口
     *
     * @return com.renpho.karma.dto.R<java.util.List < com.renpho.erp.bpm.client.process.vo.ProcessInstanceUserVo>>
     * <AUTHOR>
     * @Date 10:59 2025/3/21
     * @Param [query]
     **/
    @NoToken
    @PostMapping("/client/process/instance/currentUser")
    R<List<ProcessInstanceUserVo>> listProcessInstanceUser(@Validated @RequestBody ProcessInstanceUserQuery query);

    /**
     * 查询流程日志接口
     *
     * @return com.renpho.karma.dto.R<java.util.List < com.renpho.erp.bpm.client.process.vo.ProcessInstanceLogVO>>
     * <AUTHOR>
     * @Date 15:23 2025/5/21
     * @Param [query]
     **/
    @NoToken
    @PostMapping("/client/process/instance/node/log/query")
    R<List<ProcessInstanceLogVO>> queryNodeLog(@Validated @RequestBody ProcessInstanceNodeLogQuery query);

    /**
     * 开启流程实例接口(内部调用)
     *
     * @return com.renpho.karma.dto.R
     * <AUTHOR>
     * @Date 14:46 2025/1/6
     * @Param [command]
     **/
    @NoToken
    @PostMapping("/client/process/instance/start/inner")
    R<String> startProcessInstanceByInner(@RequestBody RemoteProcessInstanceStartCommand command);
}
