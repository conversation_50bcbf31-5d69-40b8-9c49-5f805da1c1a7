package com.renpho.erp.tms.domain.logisticsmode;


import com.renpho.erp.tms.domain.warehouse.WarehouseId;
import org.jmolecules.ddd.integration.AssociationResolver;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 物流方式查询接口
 *
 * <AUTHOR>
 * @since 2025/8/28
 */
public interface LogisticsModeLookup extends AssociationResolver<LogisticsMode, LogisticsModeId> {

    /**
     * 根据代码查询物流方式
     *
     * @param code 物流服务代码
     * @return 物流方式
     * <AUTHOR>
     * @since 2025/8/28
     */
    Optional<LogisticsMode> findByCode(String code);

    /**
     * 根据类型查询物流方式
     *
     * <AUTHOR>
     * @since 2025/8/28
     */
    List<LogisticsMode> findByType(String type);

    /**
     * 根据仓库ID查询物流方式
     *
     * <AUTHOR>
     * @since 2025/8/28
     */
    List<LogisticsMode> findByWarehouseIds(Collection<WarehouseId> warehouseIds);

}
