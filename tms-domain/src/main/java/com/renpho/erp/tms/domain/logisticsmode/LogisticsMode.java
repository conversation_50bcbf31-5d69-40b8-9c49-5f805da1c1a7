package com.renpho.erp.tms.domain.logisticsmode;

import com.renpho.erp.tms.domain.operator.Operator;
import com.renpho.erp.tms.domain.warehouse.Warehouse;
import com.renpho.erp.tms.domain.warehouse.WarehouseId;
import lombok.Data;
import org.jmolecules.ddd.types.AggregateRoot;

import java.io.Serial;
import java.io.Serializable;

/**
 * 物流方式字典表 Domain
 *
 * <AUTHOR>
 * @since 2025/8/28
 */
@Data
public class LogisticsMode implements AggregateRoot<LogisticsMode, LogisticsModeId>, Serializable {

    @Serial
    private static final long serialVersionUID = 3708103515315954171L;

    private final LogisticsModeId id;

    /**
     * 物流服务代码
     */
    private String code;

    /**
     * 服务商的物流方式名称中文
     */
    private String nameCn;

    /**
     * 服务商的物流方式名称英文
     */
    private String nameEn;

    /**
     * 订单类型
     */
    private String type;

    /**
     * 可用仓库id（做过滤）
     */
    private WarehouseId availableWarehouseId;

    /**
     * 可用仓库code（做过滤）
     */
    private String availableWarehouseCode;

    private Warehouse availableWarehouse;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建信息
     */
    private Operator created;

    /**
     * 更新信息
     */
    private Operator updated;
}
