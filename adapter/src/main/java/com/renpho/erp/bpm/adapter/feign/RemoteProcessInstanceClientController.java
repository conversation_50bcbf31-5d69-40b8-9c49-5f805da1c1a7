package com.renpho.erp.bpm.adapter.feign;

import com.renpho.erp.bpm.application.service.approval.IApprovalQueryService;
import com.renpho.erp.bpm.application.service.process.instance.IRemoteProcessInstanceService;
import com.renpho.erp.bpm.client.feign.RemoteProcessInstanceClient;
import com.renpho.erp.bpm.client.process.command.RemoteProcessInstanceStartCommand;
import com.renpho.erp.bpm.client.process.query.ProcessInstanceNodeLogQuery;
import com.renpho.erp.bpm.client.process.query.ProcessInstanceUserQuery;
import com.renpho.erp.bpm.client.process.vo.ProcessInstanceLogVO;
import com.renpho.erp.bpm.client.process.vo.ProcessInstanceRecordVO;
import com.renpho.erp.bpm.client.process.vo.ProcessInstanceUserVo;
import com.renpho.erp.security.annotation.Inner;
import com.renpho.karma.dto.R;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 远程服务-流程实例客户端控制类
 *
 * <AUTHOR>
 * @Date 2024/10/31 15:53
 **/
@RestController
public class RemoteProcessInstanceClientController implements RemoteProcessInstanceClient {

    @Resource
    private IRemoteProcessInstanceService remoteProcessInstanceService;

    @Resource
    private IApprovalQueryService approvalQueryService;

    @Override
    public R<String> startProcessInstance(RemoteProcessInstanceStartCommand command) {
        return R.success(remoteProcessInstanceService.startProcessInstance(command));
    }

    @Override
    @Inner
    public R<ProcessInstanceRecordVO> getProcessInstance(String processInstanceId) {
        return R.success(remoteProcessInstanceService.getProcessInstance(processInstanceId));
    }

    @Override
    public R<List<ProcessInstanceUserVo>> listProcessInstanceUser(ProcessInstanceUserQuery query) {
        return R.success(remoteProcessInstanceService.listProcessInstanceUser(query));
    }

    @Override
    public R<List<ProcessInstanceLogVO>> queryNodeLog(ProcessInstanceNodeLogQuery query) {
        List<ProcessInstanceLogVO> list = approvalQueryService.queryProcessInstanceNodeLog(query);
        return R.success(list);
    }

    @Override
    @Inner
    public R<String> startProcessInstanceByInner(RemoteProcessInstanceStartCommand command) {
        return R.success(remoteProcessInstanceService.startProcessInstance(command));
    }
}
