package com.renpho.erp.tms.application.logisticsmode;

import com.renpho.erp.tms.domain.logisticsmode.LogisticsMode;
import com.renpho.erp.tms.domain.logisticsmode.LogisticsModeId;
import com.renpho.erp.tms.domain.logisticsmode.LogisticsModeLookup;
import com.renpho.erp.tms.domain.warehouse.WarehouseId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 物流方式查询服务
 *
 * <AUTHOR>
 * @since 2025/8/28
 */
@Service
@Validated
@RequiredArgsConstructor
public class LogisticsModeQueryService {

    private final LogisticsModeLookup logisticsModeLookup;

    /**
     * 根据ID查询物流方式
     *
     * @param id 物流方式ID
     * @return 物流方式
     * <AUTHOR>
     * @since 2025/8/25
     */
    public Optional<LogisticsMode> findById(LogisticsModeId id) {
        return logisticsModeLookup.findById(id);
    }

    /**
     * 根据ID查询物流方式
     *
     * @param id 物流方式ID
     * @return 物流方式
     * <AUTHOR>
     * @since 2025/8/25
     */
    public Optional<LogisticsMode> findAll(LogisticsModeId id) {
        return logisticsModeLookup.findById(id);
    }

    /**
     * 根据代码查询物流方式
     *
     * @param code 物流服务代码
     * @return 物流方式
     * <AUTHOR>
     * @since 2025/8/25
     */
    public Optional<LogisticsMode> findByCode(String code) {
        return logisticsModeLookup.findByCode(code);
    }

    /**
     * 根据订单类型查询物流方式列表
     *
     * @param type 订单类型
     * @return 物流方式列表
     * <AUTHOR>
     * @since 2025/8/25
     */
    public List<LogisticsMode> findByType(String type) {
        return logisticsModeLookup.findByType(type);
    }

    /**
     * 根据仓库ID查询物流方式列表
     *
     * @param warehouseId 仓库ID
     * @return 物流方式列表
     * <AUTHOR>
     * @since 2025/8/25
     */
    public List<LogisticsMode> findByWarehouseId(WarehouseId warehouseId) {
        return logisticsModeLookup.findByWarehouseIds(List.of(warehouseId));
    }

    /**
     * 根据仓库ID查询物流方式列表
     *
     * @param warehouseIds 仓库ID集合
     * @return 物流方式列表
     * <AUTHOR>
     * @since 2025/8/25
     */
    public List<LogisticsMode> findByWarehouseIds(Collection<WarehouseId> warehouseIds) {
        return logisticsModeLookup.findByWarehouseIds(warehouseIds);
    }

}
