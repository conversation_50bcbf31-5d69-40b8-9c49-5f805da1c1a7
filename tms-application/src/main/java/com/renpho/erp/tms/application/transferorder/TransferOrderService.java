package com.renpho.erp.tms.application.transferorder;

import com.renpho.erp.apiproxy.eccang.yunwms.model.transfer.GetTransferOrderInfoResponse;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.tms.application.inbound.InboundRecordQueryService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.domain.inbound.*;
import com.renpho.erp.tms.domain.transferorder.*;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.erp.tms.infrastructure.common.constant.YunWmsConstant;
import com.renpho.erp.tms.infrastructure.common.oplog.LogModule;
import com.renpho.erp.tms.infrastructure.persistence.inbound.po.converter.InboundRecordConverter;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.repository.TransferOrderItemRepository;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.inbound.AmazonInboundClient;
import com.renpho.erp.tms.infrastructure.remote.inventory.repository.InventoryRepository;
import com.renpho.erp.tms.infrastructure.util.DateUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/8/23
 */
@Service
@RequiredArgsConstructor
public class TransferOrderService {

    private final TransferOrderRepository transferOrderRepository;
    private final TransferOrderItemRepository transferOrderItemRepository;
    private final TransferOrderLookup transferOrderLookup;
    private final InboundRecordQueryService inboundRecordQueryService;
    private final InboundRecordRepository inboundRecordRepository;
    private final InboundRecordConverter inboundRecordConverter;
    private final PushTaskService pushTaskService;
    private final AmazonInboundClient amazonInboundClient;
    private final InventoryRepository inventoryRepository;

    @Transactional(rollbackFor = Exception.class)
    public TransferOrder add(TransferOrder command) {
        inventoryRepository.lock(command);
        return transferOrderRepository.add(command);
    }

    /**
     * 根据TS ID列表查询TS单信息
     *
     * @param tsIds TS ID列表
     * @return TS单信息
     */
    public List<TransferOrder> findByIds(List<TransferOrderId> tsIds) {
        List<TransferOrder> domains = transferOrderLookup.findByIds(tsIds);
        //findAssociations(domains);
        return domains;
    }

    /**
     * 根据TR的ID集合查询tr上下文信息
     *
     * @param idList TR的ID集合
     * @return TR单信息
     */
    public Map<Integer, TransferOrder> getTsMapByIds(List<Integer> idList) {
        List<TransferOrderId> tsIds = idList.stream().map(TransferOrderId::new).toList();
        List<TransferOrder> tsList = this.findByIds(tsIds);
        return tsList.stream().collect(Collectors.toMap(TransferOrder::sourceId, a -> a));
    }

    @Transactional(rollbackFor = Exception.class)
    public TransferOrder update(TransferOrder command) {
        // TODO 解锁库存
        inventoryRepository.unlock(command);
        // TODO 锁定库存
        inventoryRepository.lock(command);
        return transferOrderRepository.update(command);
    }

    /**
     * 根据TS状态查询TS单信息
     *
     * @param statusList    TS状态列表
     * @param warehouseType 仓库类型
     * @return TS单信息
     */
    public List<TransferOrderData> selectTsListByStatusList(List<TransferOrderStatus> statusList, WarehouseProviderType warehouseType) {
        return transferOrderRepository.selectTsListByShipStatusList(statusList, warehouseType);
    }

    @LogRecord(module = LogModule.TRANSFER_ODER, type = LogModule.CommonDesc.EDIT_OPERATOR, desc = LogModule.TransferOrder.CANCEL_TS_DESC)
    public void clearShipmentIdById(TransferOrderId id) {
        Optional<TransferOrder> oldData = transferOrderLookup.findById(id);
        transferOrderRepository.clearShipmentIdById(id);
        Optional<TransferOrder> newData = transferOrderLookup.findById(id);
        LogRecordContextHolder.putRecordData(String.valueOf(id), oldData, newData);
    }

    public void handleTransferOrderData(GetTransferOrderInfoResponse data,
                                        TransferOrder ts,
                                        InboundRequestHistoryId historyId) {
        Integer status = Integer.parseInt(data.getStatus());
        if (!YunWmsConstant.TransferOrderStatus.COMPLETED.equals(status)) {
            return;
        }


        //将itemData按照FulfillmentNetworkSKU进行分组，返回一个Map，key为FulfillmentNetworkSKU，value为InboundShipmentItem
        Map<String, GetTransferOrderInfoResponse.Inbound> inboundDataMap = data.getInboundList().stream().collect(
                Collectors.toMap(
                        GetTransferOrderInfoResponse.Inbound::getSkuDesc,
                        Function.identity(),
                        (v, v2) -> v
                )
        );

        //获取签收时间
        String receivedTimeStr =
                data.getReceiveRecords().stream().findFirst()
                        .map(GetTransferOrderInfoResponse.ReceiveRecords::getEcCreateTime)
                        .orElse("0000-00-00 00:00:00");
        LocalDateTime receivedTime = DateUtils.toUtcLocalDateTime(receivedTimeStr);

        for (TransferOrderItem tsItem : ts.getItems()) {
            if (!inboundDataMap.containsKey(tsItem.getPsku())) {
                continue;
            }
            GetTransferOrderInfoResponse.Inbound shipmentItem = inboundDataMap.get(tsItem.getPsku());

            // 查询上一次记录，用于差异计算
            InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tsItem).orElse(null);

            processItem(shipmentItem, tsItem, historyId, lastRecord, receivedTime);


            //处理完签收逻辑后，处理 TS 和 TS Item 的更新
            transferOrderItemRepository.batchUpdate(ts.getItems());

            //todo TS单状态记录
            ts.setStatus(TransferOrderStatus.RECEIVING);
            ts.setStatus(TransferOrderStatus.RECEIVED);

            ts.setSyncApiStatus(SyncApiStatus.SUCCESS);
            //更新TS单
            transferOrderRepository.updateById(ts);
        }

    }


    /**
     * 处理单个ITEM 的签收与上架逻辑，并记录入库信息。
     *
     * @param shipmentItem ShipmentItem
     * @param tsItem       TS Item
     * @param historyId    历史ID
     * @param lastRecord   上一次记录
     */
    private void processItem(GetTransferOrderInfoResponse.Inbound shipmentItem,
                             TransferOrderItem tsItem,
                             InboundRequestHistoryId historyId,
                             InboundRecord lastRecord,
                             LocalDateTime receivedTime) {

        // 构建新的入库记录对象
        InboundRecord record = inboundRecordConverter.transferOrderItemToInboundRecord(tsItem);
        record.setWarehouseType(WarehouseProviderType.FBA);
        record.setRequestHistoryId(historyId);

        Integer receivedQty = tsItem.getQty(); // 实际签收数量默认为发货数量
        Integer putawayQty = calculateReceivedQuantity(shipmentItem, tsItem); // 上架数量

        //签收
        boolean haveNewReceived = record.buildReceivedInfo(receivedQty, lastRecord);
        record.setReceivedTime(receivedTime);

        //上架
        boolean haveNewPutaway = record.buildPutawayInfo(putawayQty, lastRecord);
        record.setPutawayTime(receivedTime);
        record.setPutawayStatus(InboundStatus.FINISH);
        record.setStatus(InboundStatus.FINISH);

        // 记录上架完成时间
        tsItem.setReceivedEndTime(receivedTime);

        // 如果是首次签收，记录时间
        if (tsItem.checkIsFirstReceived()) {
            tsItem.setReceivedStartTime(receivedTime);
        }

        // 更新 TS Item 的签收和上架数量
        tsItem.setReceivedQty(receivedQty);
        tsItem.setPutawayQty(putawayQty);
        tsItem.calculateTheDifferenceValue(tsItem.getReceivedQty(), tsItem.getPutawayQty());
        // 如果本次存在新增的签收或上架记录，则落库并更新状态
        if (haveNewReceived || haveNewPutaway) {
            // 保存签收/上架记录
            inboundRecordRepository.add(record);
        }
    }

    /**
     * 计算签收数量
     */
    private int calculateReceivedQuantity(GetTransferOrderInfoResponse.Inbound shipmentItem, TransferOrderItem tsItem) {
        //签收数量：receiveNum * TS的装箱数量
        return Integer.parseInt(shipmentItem.getReceiveNum()) * tsItem.getProduct().getQuantityPerBox();
    }

}
