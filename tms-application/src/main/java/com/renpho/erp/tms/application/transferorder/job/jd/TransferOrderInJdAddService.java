package com.renpho.erp.tms.application.transferorder.job.jd;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.apiproxy.jd.fop.JdResponse;
import com.renpho.erp.apiproxy.jd.fop.ProxyRoute;
import com.renpho.erp.apiproxy.jd.fop.api.FopInstockApi;
import com.renpho.erp.apiproxy.jd.fop.model.instock.DeliveryInStockRequest;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transferorder.TransferOrderService;
import com.renpho.erp.tms.application.transferorder.job.common.TransferOrderCommonService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;

import com.renpho.erp.tms.domain.exception.DingTalkWarning;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transferorder.*;

import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.domain.transportrequest.dto.WarehouseData;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * TS-目的仓为JD-创建入库单任务.
 *
 * <AUTHOR>
 * @since 2025/8/27
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransferOrderInJdAddService {

    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransferOrderCommonService transferOrderCommonService;
    private final TransferOrderService transferOrderService;
    private final PushTaskService pushTaskService;

    private final TransferOrderRepository transferOrderRepository;
    private final FopInstockApi fopInstockApi;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.JD;

//    @EventListener(ApplicationReadyEvent.class)
//    @Transactional(rollbackFor = Exception.class)
//    public void test() {
//        //createInboundJdTask(List.of("TR2507310002", "TR2507310003"));
//        //doPalletJd(List.of("TR2507310002", "TR2507310003"));
//    }

    /**
     * TS-目的仓为JD-入库单任务生成.
     */
    @Lock4j(name = "transfer:request:jd:inbound:add:basic")
    //@Transactional(rollbackFor = Exception.class)
    public void createInboundJdTask(List<String> trNoList) {
        try {
            log.info("TS-目的仓为JD的定时器任务开始");

            // 状态是待创建入库单
            TransferOrderStatus status = TransferOrderStatus.WAIT_CREATE_INBOUND;
            List<TransferOrderData> tsDataList = transferOrderCommonService.selectTsListByStatus(status.getValue(), warehouseType,false);

            // 外部参数
            tsDataList = transferOrderCommonService.fillExterWithTsStatus(status, tsDataList, trNoList);

            // 生成入库单推送任务
            if (!tsDataList.isEmpty()) {
                // 获取ts上下文信息
                Map<Integer, TransferOrder> tsMap = transferOrderService.getTsMapByIds(tsDataList.stream().map(TransferOrderData::getId).toList());

                for (TransferOrderData tsData : tsDataList) {
                    try {
                        TransferOrder ts = tsMap.get(tsData.getId());
                        if (ts == null) {
                            log.error("TS-目的仓为JD-创建入库单任务生成异常, ts上下文找不到, tsId={}", tsData.getId());
                            continue;
                        }
                        // 目的仓限制
                        if (warehouseType != ts.findWarehouseProviderType()) {
                            continue;
                        }

                        // 故意抛异常
                        // log.info((6/0)+"");

                        // 生成推送任务
                        PushTask trPushTask = pushTaskService.createInbound(ts, PushTaskStatus.PENDING,0,null);

                        // 执行入库单创建
                        pushTaskService.execute(() -> this.deliveryInstock(ts), trPushTask);
                    } catch (Exception e) {
                        log.error("TS-目的仓为JD的定时器任务异常, 入库单推送任务生成失败, tsId={}", tsData.getId(), e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("TS-目的仓为JD的定时器任务异常", e);
        }
    }


    /**
     * 执行入库单创建
     *
     * @param ts         ts上下文
     */
    private Boolean deliveryInstock(TransferOrder ts) {
        WarehouseData warehouseData = transferOrderCommonService.getJDWarehouse(ts.getDestWarehouse().getId().id());
        if (warehouseData == null) {
            log.error("TS-目的仓为JD的定时器任务异常, 无法获取客户编码, tsId={}, destWarehouseId={}", ts.getId(), ts.getDestWarehouse().getId().id());
            return false;
        }

        ProxyRoute proxyRoute = new ProxyRoute();
        proxyRoute.setCustomerCode(warehouseData.getCustomerCode());

        // 正常来说只传应用参数，不传认证参数
        DeliveryInStockRequest instockDTO = new DeliveryInStockRequest();
        // ims配置的 ownerCode / warehouseCode 也是拿IMS的
        instockDTO.setCargoOwnerId(Long.parseLong(warehouseData.getOwnerCode()));
        instockDTO.setCustomerBillCode(ts.getTsNo());
        instockDTO.setInstockType(10);
        instockDTO.setCustomerCode(warehouseData.getCustomerCode());
        instockDTO.setWarehouseCode(warehouseData.getWarehouseCode());

        List<TransferOrderItem> items = ts.getItems();

        // 若TS单的平台为Amazon的并且仓库是“CA-京东仓”(沟通结果code = JDCA001)，则传1 ; 其他的则不传值；
        if (ts.getSalesChannel().getChannelName().equals("Amazon") && ts.getDestWarehouse().getCode().equals("JDCA001")) {
            instockDTO.setPoReturnMode(1);
            instockDTO.setInstockDeliveryModel(2);

            // 按箱
            List<DeliveryInStockRequest.InstockCartonDTO> cartonList = new ArrayList<>();
            DeliveryInStockRequest.InstockCartonDTO cartonDto = new DeliveryInStockRequest.InstockCartonDTO();
            cartonDto.setCartonCode(ts.getTsNo());

            List<DeliveryInStockRequest.InstockCartonDetailDTO> cartonDetailList = new ArrayList<>();
            for(TransferOrderItem item : items){
                DeliveryInStockRequest.InstockCartonDetailDTO cartonDetailDto = new DeliveryInStockRequest.InstockCartonDetailDTO();
                cartonDetailDto.setSku(item.getFnSku());  // ts.getFnSku() 是商品编码 czhgoodys002
                cartonDetailDto.setQty(item.getBoxQty().intValue());
                cartonDetailDto.setInstockCartonCode(item.getTsNo());
                cartonDetailList.add(cartonDetailDto);
                cartonList.add(cartonDto);
            }
            cartonDto.setCartonDetailList(cartonDetailList);
            instockDTO.setCartonList(cartonList);
        } else {
            instockDTO.setInstockDeliveryModel(0);

            // 按单 instockDetailList
            List<DeliveryInStockRequest.InstockDetailDTO> instockDetailList = new ArrayList<>();
            for(TransferOrderItem item : items){
                DeliveryInStockRequest.InstockDetailDTO instockDetailDto = new DeliveryInStockRequest.InstockDetailDTO();
                instockDetailDto.setSku(item.getFnSku());  // ts.getFnSku() 是商品编码 czhgoodys002
                instockDetailDto.setQuantity(item.getQty());
                instockDetailList.add(instockDetailDto);
            }
            instockDTO.setInstockDetailList(instockDetailList);
        }

        instockDTO.setArriveWarehouseType(3);  // 卡派到仓
        instockDTO.setArriveWarehouseCode(ts.getTsNo()); // 填ts单号

        log.info("TS-目的仓为JD创建入库单, 参数={}", JSON.toJSONString(instockDTO));
        R<JdResponse<String>> result = fopInstockApi.deliveryInstock(proxyRoute, instockDTO);
        log.info("TS-目的仓为JD创建入库单, 响应={}", JSON.toJSONString(result));
        Integer resultCode = 200;
        Object body = null;
        if (result != null && result.getData()!=null && result.getData().getResponse()!=null) {
            resultCode = result.getData().getResponse().getCode();
            if(result.getData().getResponse().getContent().isSuccess()){
                body = result.getData().getResponse().getContent();
                String iaNo = result.getData().getResponse().getContent().getData();

                // 创建成功，保存 iaNo
                ts.setShipmentId(iaNo);
                ts.setSyncApiStatus(SyncApiStatus.SUCCESS);
                transferOrderRepository.updateById(ts);

                // 3. 生成箱唛推送任务(这个跟tr生命周期重复，所以注释掉)
                // transportRequestCommonService.createCartonFile(tr, warehouseType);

                // 记录请求历史
                inboundRequestHistoryService.add(ts, WarehouseProviderType.JD, null, instockDTO, resultCode, null, result);
            } else {
                // 记录请求历史
                inboundRequestHistoryService.add(ts, WarehouseProviderType.JD, null, instockDTO, resultCode, null, result);
                throw new DingTalkWarning("API创建入库单失败，原因：%s".formatted(result.getData().getResponse().getContent().getErrorMsg()));
            }
        } else {
            // 记录请求历史
            inboundRequestHistoryService.add(ts, WarehouseProviderType.JD, null, instockDTO, resultCode, null, result);
            throw new DingTalkWarning("API创建入库单失败，原因：%s".formatted("返回数据为空, tsId = "+ts.getId().id()));
        }
        return true;
    }

    /**
     * 执行: TS-目的仓为JD-箱唛任务.
     * @param tsNoList ts单号集合，用于异常重试的参数触发机制
     */
    @Lock4j(name = "transfer:request:jd:inbound:add:pallet")
    @Transactional(rollbackFor = Exception.class)
    public void doPalletJd(List<String> tsNoList) {
        transferOrderCommonService.doingPallet(warehouseType, tsNoList);
    }

}
