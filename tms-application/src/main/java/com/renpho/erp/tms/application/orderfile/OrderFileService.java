package com.renpho.erp.tms.application.orderfile;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.openai.gpt.service.GptService;
import com.renpho.erp.tms.application.transportorder.TransportOrderCheckService;
import com.renpho.erp.tms.application.transportorder.TransportOrderService;
import com.renpho.erp.tms.application.transportordercustoms.TransportOrderCustomsService;
import com.renpho.erp.tms.domain.common.AiDescConstants;
import com.renpho.erp.tms.domain.common.CustomsTaxReceiptCoordinateEnum;
import com.renpho.erp.tms.domain.exception.BusinessException;
import com.renpho.erp.tms.domain.file.FileId;
import com.renpho.erp.tms.domain.file.FileInfo;
import com.renpho.erp.tms.domain.orderfile.*;
import com.renpho.erp.tms.domain.transportorder.TransportOrderId;
import com.renpho.erp.tms.infrastructure.common.oplog.LogModule;
import com.renpho.erp.tms.infrastructure.persistence.orderfile.po.converter.OrderFileConverter;
import com.renpho.erp.tms.infrastructure.remote.file.repository.FileLookup;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.ai.content.Media;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.MimeType;
import org.springframework.util.MimeTypeUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.*;

/**
 * 附件
 *
 * <AUTHOR> Zheng
 * @date 2025/6/16
 */
@Slf4j
@Service
@AllArgsConstructor
public class OrderFileService {

    private final OrderFileConverter orderFileConverter;

    private final TransportOrderCustomsService transportOrderCustomsService;
    private final TransportOrderService transportOrderService;

    private final OrderFileLookup orderFileLookup;
    private final OrderFileRepository orderFileRepository;

    private final TransportOrderCheckService transportOrderCheckService;

    private final FileLookup fileLookup;

    private final GptService gptService;

    public List<OrderFile> allTypeList(Integer id, BusinessTypeEnum businessType) {
        List<OrderFile> orderFileList = orderFileLookup.findListByOrderId(id, businessType, null);
        // 根据业务类型补齐文件类型
        List<FileTypeEnum> fileTypeEnumList = FileTypeEnum.getByType(businessType);
        List<FileTypeEnum> fileTypeList = orderFileList.stream().map(OrderFile::getFileType).distinct().toList();
        if (BusinessTypeEnum.TO.equals(businessType) && fileTypeList.size() != fileTypeEnumList.size()) {
            for (FileTypeEnum fileTypeEnum : fileTypeEnumList) {
                if (!fileTypeList.contains(fileTypeEnum)) {
                    OrderFile orderFile = new OrderFile(null);
                    orderFile.setFileType(fileTypeEnum);
                    orderFileList.add(orderFile);
                }
            }
        }
        return orderFileList;
    }

    public List<OrderFile> list(OrderFile query) {
        return orderFileLookup.findListByOrderId(query.getOrderId(), BusinessTypeEnum.TO, null);
    }

    /**
     * 新增业务单据关联的附件
     *
     * @param orderFile 多个文件
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(OrderFile orderFile) {
        List<OrderFileInfo> fileInfoList = orderFile.getFileInfos();
        List<OrderFile> existOrderFileList = orderFileLookup.findListByOrderId(orderFile.getOrderId(), orderFile.getBusinessType(),
                orderFile.getFileType());
        // 校验
        int maxSize = 10;
        if (BusinessTypeEnum.TO.equals(orderFile.getBusinessType()) && (fileInfoList.size() + existOrderFileList.size()) > maxSize) {
            throw new BusinessException("to.file.size.max", orderFile.getFileType().desc());
        }
        // 新增附件
        List<OrderFile> orderFileList = new ArrayList<>();
        for (OrderFileInfo orderFileInfo : fileInfoList) {
            orderFileList.add(orderFileConverter.toDomain(orderFile, orderFileInfo));
        }
        List<OrderFile> dataOrderFileList = orderFileRepository.batchSave(orderFileList);
        if (BusinessTypeEnum.TO.equals(orderFile.getBusinessType())) {
            this.toAfter(orderFile, dataOrderFileList);
        }

        // 头程-文件异常提醒-解除
        if (BusinessTypeEnum.TO == orderFile.getBusinessType()) {
            transportOrderCheckService.dismissCheckReminderByFileType(orderFile.getOrderId(), orderFile.getFileType());
        }
        return true;
    }

    /**
     * TO头程单操作
     *
     * @param orderFile     上传信息
     * @param orderFileList 上传后附件信息
     */
    public void toAfter(OrderFile orderFile, List<OrderFile> orderFileList) {
        if (FileTypeEnum.CUSTOMS_TAX_RECEIPT.equals(orderFile.getFileType())) {
            // 新增海关税单号
            transportOrderCustomsService.batchSave(orderFileList.stream().filter(e -> StringUtils.isNotBlank(e.getCustomsNo())).toList());
        }
        // 校验所有文件类型都上传完后，更新主表信息
        Map<FileTypeEnum, Integer> fileTypeMap = orderFileRepository.groupFileType(orderFile.getBusinessType(), orderFile.getOrderId());
        if (CollUtil.isNotEmpty(fileTypeMap) && fileTypeMap.size() == FileTypeEnum.toTypeSize()) {
            transportOrderService.updateDocComplete(new TransportOrderId(orderFile.getOrderId()));
        }
        // 记录日志
        SpringUtil.getBean(OrderFileService.class).recordToLog(orderFile.getOrderId(), orderFileList);
    }

    /**
     * 记录头程单日志
     *
     * @param orderId       头程单id
     * @param orderFileList 附件
     */
    @LogRecord(module = LogModule.TRANSPORT_ORDER, type = LogModule.CommonDesc.UPLOAD_FILE_OPERATOR,
            desc = LogModule.CommonDesc.UPLOAD_FILE_DESC)
    public void recordToLog(Integer orderId, List<OrderFile> orderFileList) {
        for (OrderFile orderFile : orderFileList) {
            LogRecordContextHolder.putRecordData(String.valueOf(orderId), null, orderFile);
        }
    }

    /**
     * AI识别文件里的海关税单号
     *
     * @param orderFileInfo 文件信息
     * @return 包含海关税单号的文件信息
     */
    public OrderFileInfo recognizeCustomsTaxReceipt(OrderFileInfo orderFileInfo) {
        try {
            Map<FileId, FileInfo> fileInfoMap = fileLookup.findByIds(List.of(orderFileInfo.getFileId()));
            if (CollUtil.isEmpty(fileInfoMap)) {
                throw new BusinessException("data.is.null");
            }
            String url = fileInfoMap.get(orderFileInfo.getFileId()).getUrl();
            String ext = FileUtil.extName(url);
            MimeType type = getType(ext);
            // 文件转为bytes
            byte[] bytes = convertImageBytes(ext, url);
            Media media = new Media(type, new ByteArrayResource(bytes));
            String aiResult = gptService.chat(AiDescConstants.CUSTOMS_TAX_RECEIPT_NO, List.of(media));
            log.info("AI识别海关税单内容结果：{}", aiResult);
            List<OrderFileAi> orderFileAiList = JSON.parseArray(
                    aiResult.replace("```json", "").replace("```", ""),
                    OrderFileAi.class);
            if (CollUtil.isNotEmpty(orderFileAiList)) {
                OrderFileAi orderFileAi = orderFileAiList.get(0);
                orderFileInfo.setCustomsNo(orderFileAi.getValue());
                orderFileInfo.setIdentifyBase64(identifyPictureBase64(orderFileAi.getCategory(), bytes));
            }
        } catch (Exception e) {
            log.error("AI识别海关税单号异常", e);
        }
        return orderFileInfo;
    }

    /**
     * 识别关键位置的图片base64格式
     * <p>
     * 按照国家模板来截取关键位置
     *
     * @param country 国家
     * @param picture 原图片字节
     * @return 关键位置
     */
    private String identifyPictureBase64(String country, byte[] picture) {
        try (ByteArrayInputStream bais = new ByteArrayInputStream(picture);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            CustomsTaxReceiptCoordinateEnum customsTaxReceiptCoordinateEnum = CustomsTaxReceiptCoordinateEnum.valueOf(country);
            BufferedImage originalImage = ImageIO.read(bais);
            // 计算实际像素位置和大小
            Float startXRatio = customsTaxReceiptCoordinateEnum.getStartXRatio();
            Float startYRatio = customsTaxReceiptCoordinateEnum.getStartYRatio();
            int x = (int) (startXRatio * originalImage.getWidth());
            int y = (int) (startYRatio * originalImage.getHeight());
            int width = (int) ((customsTaxReceiptCoordinateEnum.getEndXRatio() - startXRatio) * originalImage.getWidth());
            int height = (int) ((customsTaxReceiptCoordinateEnum.getEndYRatio() - startYRatio) * originalImage.getHeight());
            // 裁剪
            BufferedImage cropped = originalImage.getSubimage(x, y, width, height);
            ImageIO.write(cropped, "png", baos);
            String base64EncodedData = Base64.getEncoder().encodeToString(baos.toByteArray());
            return "data:image/png;base64," + base64EncodedData;
        } catch (Exception e) {
            log.warn("AI识别海关税单号未匹配到国家：{}", country, e);
        }
        return null;
    }

    /**
     * 获取文件类型
     *
     * @param ext 文件扩展名
     * @return 文件类型
     */
    private MimeType getType(String ext) {
        return switch (ext) {
            case FileTypeConstants.JPG, FileTypeConstants.JPEG -> MimeTypeUtils.IMAGE_JPEG;
            case FileTypeConstants.PNG, FileTypeConstants.PDF -> MimeTypeUtils.IMAGE_PNG;
            default -> {
                throw new BusinessException("file.type.unsupported");
            }
        };
    }

    /**
     * 转化URL为图片byte
     *
     * @param ext  文件扩展名
     * @param url  文件url
     * @return 图片
     */
    public byte[] convertImageBytes(String ext, String url) {
        try (HttpResponse httpResponse = HttpUtil.createGet(url).execute()) {
            if (FileTypeConstants.PDF.equals(ext)) {
                // PDF截取第一页
                int page = 1;
                try (PDDocument document = PDDocument.load(httpResponse.bodyBytes())) {
                    PDFRenderer renderer = new PDFRenderer(document);
                    BufferedImage[] images = new BufferedImage[page];
                    int totalHeight = 0;
                    int maxWidth = 0;
                    for (int i = 0; i < page; i++) {
                        BufferedImage image = renderer.renderImageWithDPI(i, 150);
                        images[i] = image;
                        totalHeight += image.getHeight();
                        maxWidth = Math.max(maxWidth, image.getWidth());
                    }
                    BufferedImage combined = new BufferedImage(maxWidth, totalHeight, BufferedImage.TYPE_INT_RGB);
                    Graphics g = combined.getGraphics();
                    int currentY = 0;
                    for (BufferedImage image : images) {
                        g.drawImage(image, 0, currentY, null);
                        currentY += image.getHeight();
                    }
                    g.dispose();
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    ImageIO.write(combined, "png", baos);
                    return baos.toByteArray();
                } catch (Exception e) {
                    log.error("PDF转PNG异常", e);
                    throw new BusinessException("file.converter.is.fail", e);
                }
            } else {
                return httpResponse.bodyBytes();
            }
        } catch (Exception e) {
            log.error("文件下载失败", e);
            throw new BusinessException("file.converter.is.fail", e);
        }
    }

    public Boolean delete(FileId fileId) {
      return  orderFileRepository.deleteByFileId(fileId);
    }
}
