package com.renpho.erp.tms.application.api.yunwms;

import com.renpho.erp.apiproxy.eccang.yunwms.model.instock.*;
import com.renpho.erp.apiproxy.eccang.yunwms.model.transfer.*;
import com.renpho.erp.ftm.client.response.FileInfoResponse;
import com.renpho.erp.tms.application.api.yunwms.strategy.YunWmsInboundOrderStrategy;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.domain.inbound.InboundRequestHistory;
import com.renpho.erp.tms.domain.transportrequest.PushTask;
import com.renpho.erp.tms.domain.warehouse.Warehouse;
import com.renpho.erp.tms.domain.warehouse.WarehouseConfig;
import com.renpho.erp.tms.domain.warehouse.WarehouseId;
import com.renpho.erp.tms.infrastructure.common.constant.CommonConstant;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.inbound.EccangInboundClient;
import com.renpho.erp.tms.infrastructure.remote.file.RemoteFileFeign;
import com.renpho.erp.tms.infrastructure.remote.warehouse.repository.WarehouseLookup;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 入库单生成上下文
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class YunWmsInboundOrderContext {

    private final EccangInboundClient eccangInboundClient;
    private final WarehouseLookup warehouseLookup;
    private final PushTaskService pushTaskService;
    private final RemoteFileFeign remoteFileFeign;

    /**
     * 执行创建转运单
     */
    public <T> void executeCreateTransferInbound(List<T> orders,
                                                 YunWmsInboundOrderStrategy<T> strategy,
                                                 WarehouseExtractor<T> warehouseExtractor) {

        List<WarehouseId> destinationIds = orders.stream()
                .map(warehouseExtractor::getDestWarehouse)
                .map(Warehouse::getId)
                .toList();
        Map<WarehouseId, WarehouseConfig> warehouseConfigMap = warehouseLookup.getWarehouseConfigMap(destinationIds);

        for (T order : orders) {
            processOrder(order, strategy, warehouseExtractor, warehouseConfigMap);
        }
    }

    private <T> void processOrder(T order,
                                  YunWmsInboundOrderStrategy<T> strategy,
                                  WarehouseExtractor<T> warehouseExtractor,
                                  Map<WarehouseId, WarehouseConfig> warehouseConfigMap) {
        PushTask inbound = strategy.createPushTask(order);
        WarehouseConfig warehouseConfig = warehouseConfigMap.get(warehouseExtractor.getDestWarehouse(order).getId());
        TransferOrderSaveRequest req = strategy.buildCreateTransferInboundRequest(order);
        InboundRequestHistory history = strategy.addRequestHistory(order, req);

        try {
            // 调API代理中心接口，创建转运单
            R<String> ret = pushTaskService.execute(() -> {
                R<String> r = eccangInboundClient.transferOrderSave(warehouseConfig.getCustomerCode(), req);
                //将异常抛出才能触发重试机制
                if (!r.isSuccess()) {
                    log.error("调用【易仓-创建转运单】接口异常：, orderNo={}，异常信息：{}", strategy.getOrderNo(order), r);
                    strategy.updateRequestHistory(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, r);
                    throw new RuntimeException("API创建入库单失败，原因：%s".formatted(r.getMessage()));
                }
                return r;
            }, inbound);


            String transferNo = ret.getData();
            strategy.updateRequestHistory(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, null, transferNo);
            //更新TR单的shipmentId
            strategy.updateOrder(order, transferNo);
        } catch (Exception e) {
            log.error("创建【极智佳转运单】失败, orderNo={}", strategy.getOrderNo(order), e);
        }

    }


    /**
     * 执行获取箱唛
     */
    public <T> void executeGetTransferOrderBoxLabel(List<T> orders,
                                                    YunWmsInboundOrderStrategy<T> strategy,
                                                    WarehouseExtractor<T> warehouseExtractor) {

        List<WarehouseId> destinationIds = orders.stream()
                .map(warehouseExtractor::getDestWarehouse)
                .map(Warehouse::getId)
                .toList();
        Map<WarehouseId, WarehouseConfig> warehouseConfigMap = warehouseLookup.getWarehouseConfigMap(destinationIds);

        for (T order : orders) {
            processBoxLabelOrder(order, strategy, warehouseExtractor, warehouseConfigMap);
        }
    }


    /**
     * 处理箱唛
     */
    private <T> void processBoxLabelOrder(T order,
                                          YunWmsInboundOrderStrategy<T> strategy,
                                          WarehouseExtractor<T> warehouseExtractor,
                                          Map<WarehouseId, WarehouseConfig> warehouseConfigMap) {
        WarehouseConfig warehouseConfig = warehouseConfigMap.get(warehouseExtractor.getDestWarehouse(order).getId());
        TransferOrderLabelRenderBoxMarkRequest req = strategy.buildBoxLabelRequest(order);
        InboundRequestHistory history = strategy.addRequestHistory(order, req);
        PushTask pushTask = strategy.getCartonFileGenerationTask(order);

        try {
            // 调API代理中心接口，获取箱唛文件
            R<String> ret = pushTaskService.execute(() -> {
                R<String> r = eccangInboundClient.transferOrderLabelRenderBoxMark(warehouseConfig.getCustomerCode(), req);
                if (!r.isSuccess()) {
                    log.error("调用【易仓-获取转运单箱唛】接口异常：, orderNo={}，异常信息：{}", strategy.getOrderNo(order), r);
                    strategy.updateRequestHistory(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, r);
                    throw new RuntimeException("API获取箱唛失败，原因：%s".formatted(r.getMessage()));
                }
                return r;
            }, pushTask);

            String cartonLabelUrl = ret.getData();
            strategy.updateRequestHistory(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, null, cartonLabelUrl);

            // 上传至S3
            R<List<FileInfoResponse>> r = remoteFileFeign.uploadByUrl(List.of(cartonLabelUrl), 1,
                    RemoteFileFeign.UPLOAD_SYSTEM, "carton-label", true);

            if (!r.isSuccess()) {
                log.error("转运单箱唛上传FTM失败:{}", r);
                throw new RuntimeException("箱唛上传FTM失败，原因：%s".formatted(r.getMessage()));
            }

            List<String> fileIds = r.getData().stream().map(FileInfoResponse::getId).toList();
            strategy.updateCartonLabelFiles(order, fileIds);
            strategy.sendCartonFilesMessage(order);
        } catch (Exception e) {
            log.error("获取【极智佳-中转-获取箱唛】失败, orderNo={}", strategy.getOrderNo(order), e);
        }
    }

    /**
     * 执行取消转运单
     */
    public <T> void executeCancelTransferOrder(List<T> orders,
                                               YunWmsInboundOrderStrategy<T> strategy,
                                               WarehouseExtractor<T> warehouseExtractor) {
        List<WarehouseId> destinationIds = orders.stream()
                .map(warehouseExtractor::getDestWarehouse)
                .map(Warehouse::getId)
                .toList();
        Map<WarehouseId, WarehouseConfig> warehouseConfigMap = warehouseLookup.getWarehouseConfigMap(destinationIds);

        for (T order : orders) {
            processCancelOrder(order, strategy, warehouseExtractor, warehouseConfigMap);
        }
    }

    private <T> void processCancelOrder(T order,
                                        YunWmsInboundOrderStrategy<T> strategy,
                                        WarehouseExtractor<T> warehouseExtractor,
                                        Map<WarehouseId, WarehouseConfig> warehouseConfigMap) {
        //构建请求参数
        WarehouseConfig warehouseConfig = warehouseConfigMap.get(warehouseExtractor.getDestWarehouse(order).getId());
        TransferOrderCancelRequest req = strategy.buildCancelTransferOrderRequest(order);
        InboundRequestHistory history = strategy.addRequestHistory(order, req);
        PushTask cancelTask = strategy.createCancelTask(order);

        try {
            R<String> ret = pushTaskService.execute(() -> {
                // 调API代理中心接口，取消入库单
                R<String> r = eccangInboundClient.transferOrderCancel(warehouseConfig.getCustomerCode(), req);
                if (!r.isSuccess()) {
                    log.error("调用【易仓-取消转运单】接口异常：, orderNo={}，异常信息：{}", strategy.getOrderNo(order), r);
                    strategy.updateRequestHistory(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, r);
                    //将异常抛出才能触发重试机制
                    throw new RuntimeException("API取消入库单失败，原因：%s".formatted(r.getMessage()));
                }
                return r;
            }, cancelTask);

            strategy.updateRequestHistory(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, null, ret.getData());
            //清空shipmentId
            strategy.clearShipmentId(order);
        } catch (Exception e) {
            log.error("作废【极智佳转运单】失败, orderNo={}", strategy.getOrderNo(order), e);
        }
    }

    /**
     * 执行拉取转运单
     */
    public <T> void executePullTransferOrder(List<T> orders,
                                             YunWmsInboundOrderStrategy<T> strategy,
                                             WarehouseExtractor<T> warehouseExtractor) {
        List<WarehouseId> destinationIds = orders.stream()
                .map(warehouseExtractor::getDestWarehouse)
                .map(Warehouse::getId)
                .toList();
        Map<WarehouseId, WarehouseConfig> warehouseConfigMap = warehouseLookup.getWarehouseConfigMap(destinationIds);

        for (T order : orders) {
            processPullOrder(order, strategy, warehouseExtractor, warehouseConfigMap);
        }
    }

    private <T> void processPullOrder(T order,
                                      YunWmsInboundOrderStrategy<T> strategy,
                                      WarehouseExtractor<T> warehouseExtractor,
                                      Map<WarehouseId, WarehouseConfig> warehouseConfigMap) {
        WarehouseConfig warehouseConfig = warehouseConfigMap.get(warehouseExtractor.getDestWarehouse(order).getId());
        //构建请求参数
        GetTransferOrderInfoRequest req = strategy.buildGetTransferOrderInfoRequest(order);
        // 保存请求记录
        InboundRequestHistory history = strategy.addRequestHistory(order, req);
        PushTask inboundPushTask = strategy.createInboundPushTask(order);

        try {
            R<GetTransferOrderInfoResponse> ret = pushTaskService.execute(() -> {
                // 调API代理中心接口，查询转运单
                R<GetTransferOrderInfoResponse> r = eccangInboundClient.getTransferOrderInfo(warehouseConfig.getCustomerCode(), req);
                if (!r.isSuccess()) {
                    log.error("调用易仓-获取转运单列表接口异常：, orderNo={}，异常信息：{}", strategy.getOrderNo(order), r);
                    strategy.updateRequestHistory(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, r);
                    //将异常抛出才能触发重试机制
                    throw new RuntimeException("API获取入库单失败，原因：%s".formatted(r.getMessage()));
                }
                return r;
            }, inboundPushTask);

            GetTransferOrderInfoResponse data = ret.getData();
            strategy.updateRequestHistory(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, null, data);
            // 处理转运单签收数据
            strategy.handleTransferOrderData(data, order, history.getId());
        } catch (Exception e) {
            log.error("拉取【极智佳转运单】失败, orderNo={}", strategy.getOrderNo(order), e);
        }
    }


    /**
     * 执行创建入库单（一件代发）
     */
    public <T> void executeCreateAsn(List<T> orders,
                                     YunWmsInboundOrderStrategy<T> strategy,
                                     WarehouseExtractor<T> warehouseExtractor) {

        List<WarehouseId> destinationIds = orders.stream()
                .map(warehouseExtractor::getDestWarehouse)
                .map(Warehouse::getId)
                .toList();
        Map<WarehouseId, WarehouseConfig> warehouseConfigMap = warehouseLookup.getWarehouseConfigMap(destinationIds);

        for (T order : orders) {
            processAsnOrder(order, strategy, warehouseExtractor, warehouseConfigMap);
        }
    }

    private <T> void processAsnOrder(T order,
                                     YunWmsInboundOrderStrategy<T> strategy,
                                     WarehouseExtractor<T> warehouseExtractor,
                                     Map<WarehouseId, WarehouseConfig> warehouseConfigMap) {
        PushTask inbound = strategy.createPushTask(order);
        WarehouseConfig warehouseConfig = warehouseConfigMap.get(warehouseExtractor.getDestWarehouse(order).getId());
        CreateAsnRequest req = strategy.buildCreateAsnRequest(order);
        InboundRequestHistory history = strategy.addRequestHistory(order, req);

        try {
            // 调API代理中心接口，创建入库单
            R<CreateAsnResponse> ret = pushTaskService.execute(() -> {
                R<CreateAsnResponse> r = eccangInboundClient.createAsn(warehouseConfig.getCustomerCode(), req);
                //将异常抛出才能触发重试机制
                if (!r.isSuccess()) {
                    log.error("调用【易仓-创建入库单】接口异常：, orderNo={}，异常信息：{}", strategy.getOrderNo(order), r);
                    strategy.updateRequestHistory(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, r);
                    throw new RuntimeException("API创建入库单失败，原因：%s".formatted(r.getMessage()));
                }
                return r;
            }, inbound);

            String receiveCode = Optional.ofNullable(ret.getData())
                    .map(CreateAsnResponse::getData)
                    .map(CreateAsnResponse.ResponseData::getReceiving_code)
                    .orElse("");
            strategy.updateRequestHistory(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, null, ret.getData());
            //更新订单的shipmentId
            strategy.updateOrder(order, receiveCode);
        } catch (Exception e) {
            log.error("创建【极智佳入库单】失败, orderNo={}", strategy.getOrderNo(order), e);
        }
    }

    /**
     * 执行获取入库单箱唛（一件代发）
     */
    public <T> void executeGetAsnBoxLabel(List<T> orders,
                                          YunWmsInboundOrderStrategy<T> strategy,
                                          WarehouseExtractor<T> warehouseExtractor) {

        List<WarehouseId> destinationIds = orders.stream()
                .map(warehouseExtractor::getDestWarehouse)
                .map(Warehouse::getId)
                .toList();
        Map<WarehouseId, WarehouseConfig> warehouseConfigMap = warehouseLookup.getWarehouseConfigMap(destinationIds);

        for (T order : orders) {
            processAsnBoxLabelOrder(order, strategy, warehouseExtractor, warehouseConfigMap);
        }
    }

    private <T> void processAsnBoxLabelOrder(T order,
                                             YunWmsInboundOrderStrategy<T> strategy,
                                             WarehouseExtractor<T> warehouseExtractor,
                                             Map<WarehouseId, WarehouseConfig> warehouseConfigMap) {
        WarehouseConfig warehouseConfig = warehouseConfigMap.get(warehouseExtractor.getDestWarehouse(order).getId());
        //构建请求参数
        GetReceivingBoxPdfByCodeRequest req = strategy.buildGetAsnBoxLabelRequest(order);
        // 保存请求记录
        InboundRequestHistory history = strategy.addRequestHistory(order, req);
        PushTask pushTask = strategy.getCartonFileGenerationTask(order);

        try {
            // 调API代理中心接口，获取箱唛文件
            R<GetReceivingBoxPdfByCodeResponse> ret = pushTaskService.execute(() -> {
                R<GetReceivingBoxPdfByCodeResponse> r = eccangInboundClient.getReceivingBoxPdfByCode(warehouseConfig.getCustomerCode(), req);
                if (!r.isSuccess()) {
                    log.error("调用易仓-获取箱唛接口异常：, orderNo={}，异常信息：{}", strategy.getOrderNo(order), r);
                    strategy.updateRequestHistory(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, r);
                    //将异常抛出才能触发重试机制
                    throw new RuntimeException("API获取箱唛失败，原因：%s".formatted(r.getMessage()));
                }
                return r;
            }, pushTask);

            String cartonLabelUrl = ret.getData().getBase64();
            strategy.updateRequestHistory(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, null, ret.getData());

            // 将箱唛上传至S3
            R<List<FileInfoResponse>> r = remoteFileFeign.uploadByUrl(List.of(cartonLabelUrl), 1,
                    RemoteFileFeign.UPLOAD_SYSTEM, "carton-label", true);

            if (!r.isSuccess()) {
                log.error("箱唛上传FTM失败:{}", r);
                throw new RuntimeException("箱唛上传FTM失败，原因：%s".formatted(r.getMessage()));
            }

            List<String> fileIds = r.getData().stream().map(FileInfoResponse::getId).toList();
            strategy.updateAsnCartonLabelFiles(order, fileIds);
            strategy.sendCartonFilesMessage(order);
        } catch (Exception e) {
            log.error("获取【极智佳入库单箱唛】失败, orderNo={}", strategy.getOrderNo(order), e);
        }
    }

    /**
     * 执行取消ASN（一件代发）
     */
    public <T> void executeCancelAsn(List<T> orders,
                                     YunWmsInboundOrderStrategy<T> strategy,
                                     WarehouseExtractor<T> warehouseExtractor) {

        List<WarehouseId> destinationIds = orders.stream()
                .map(warehouseExtractor::getDestWarehouse)
                .map(Warehouse::getId)
                .toList();
        Map<WarehouseId, WarehouseConfig> warehouseConfigMap = warehouseLookup.getWarehouseConfigMap(destinationIds);

        for (T order : orders) {
            processCancelAsnOrder(order, strategy, warehouseExtractor, warehouseConfigMap);
        }
    }

    private <T> void processCancelAsnOrder(T order,
                                           YunWmsInboundOrderStrategy<T> strategy,
                                           WarehouseExtractor<T> warehouseExtractor,
                                           Map<WarehouseId, WarehouseConfig> warehouseConfigMap) {
        WarehouseConfig warehouseConfig = warehouseConfigMap.get(warehouseExtractor.getDestWarehouse(order).getId());
        //构建请求参数
        CancelAsnRequest req = strategy.buildCancelAsnRequest(order);
        // 保存请求记录
        InboundRequestHistory history = strategy.addRequestHistory(order, req);
        PushTask cancelTask = strategy.createCancelAsnTask(order);

        try {
            R<CancelAsnResponse> ret = pushTaskService.execute(() -> {
                // 调API代理中心接口，取消入库单
                R<CancelAsnResponse> r = eccangInboundClient.cancelAsn(warehouseConfig.getCustomerCode(), req);
                if (!r.isSuccess()) {
                    log.error("调用【易仓-取消入库单】接口异常：, orderNo={}，异常信息：{}", strategy.getOrderNo(order), r);
                    strategy.updateRequestHistory(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, r);
                    //将异常抛出才能触发重试机制
                    throw new RuntimeException("API取消入库单失败，原因：%s".formatted(r.getMessage()));
                }
                return r;
            }, cancelTask);

            strategy.updateRequestHistory(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, null, ret.getData());
            //清空shipmentId
            strategy.clearShipmentId(order);
        } catch (Exception e) {
            log.error("作废【极智佳入库单】失败, orderNo={}", strategy.getOrderNo(order), e);
        }
    }

    /**
     * 执行拉取ASN列表（一件代发）
     */
    public <T> void executePullAsnList(List<T> orders,
                                       YunWmsInboundOrderStrategy<T> strategy,
                                       WarehouseExtractor<T> warehouseExtractor) {

        List<WarehouseId> destinationIds = orders.stream()
                .map(warehouseExtractor::getDestWarehouse)
                .map(Warehouse::getId)
                .toList();
        Map<WarehouseId, WarehouseConfig> warehouseConfigMap = warehouseLookup.getWarehouseConfigMap(destinationIds);

        for (T order : orders) {
            processPullAsnListOrder(order, strategy, warehouseExtractor, warehouseConfigMap);
        }
    }

    private <T> void processPullAsnListOrder(T order,
                                             YunWmsInboundOrderStrategy<T> strategy,
                                             WarehouseExtractor<T> warehouseExtractor,
                                             Map<WarehouseId, WarehouseConfig> warehouseConfigMap) {
        WarehouseConfig warehouseConfig = warehouseConfigMap.get(warehouseExtractor.getDestWarehouse(order).getId());
        //构建请求参数
        CetAsnListRequest req = strategy.buildGetAsnListRequest(order);
        // 保存请求记录
        InboundRequestHistory history = strategy.addRequestHistory(order, req);
        PushTask inboundPushTask = strategy.createInboundPushTask(order);

        try {
            R<GetAsnListResponse> ret = pushTaskService.execute(() -> {
                // 调API代理中心接口，查询入库单
                R<GetAsnListResponse> r = eccangInboundClient.getAsnList(warehouseConfig.getCustomerCode(), req);
                if (!r.isSuccess()) {
                    log.error("调用易仓-获取入库单列表接口异常：, orderNo={}，异常信息：{}", strategy.getOrderNo(order), r);
                    strategy.updateRequestHistory(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, r);
                    //将异常抛出才能触发重试机制
                    throw new RuntimeException("API获取入库单失败，原因：%s".formatted(r.getMessage()));
                }
                return r;
            }, inboundPushTask);

            strategy.updateRequestHistory(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, null, ret.getData());
            List<GetAsnListResponse.AsnListData> dataList = ret.getData().getData();
            strategy.handleAsnListData(dataList, order, history.getId());
        } catch (Exception e) {
            log.error("拉取【极智佳入库单】失败, orderNo={}", strategy.getOrderNo(order), e);
        }
    }

    @FunctionalInterface
    public interface WarehouseExtractor<T> {
        Warehouse getDestWarehouse(T order);
    }
}
