package com.renpho.erp.tms.application.transferorder;

import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.orderfile.BusinessTypeEnum;
import com.renpho.erp.tms.domain.orderfile.OrderFile;
import com.renpho.erp.tms.domain.orderfile.OrderFileLookup;
import com.renpho.erp.tms.domain.transferorder.*;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.karma.dto.Paging;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/8/23
 */
@Service
@RequiredArgsConstructor
public class TransferOrderQueryService {

    private final TransferOrderLookup transferOrderLookup;
    private final TransferOrderCommentLookup transferOrderCommentLookup;
    private final TransferOrderStatusHistoryLookup transferOrderStatusHistoryLookup;
    private final OrderFileLookup orderFileLookup;
    private final TransferOrderCustomerLookup transferOrderCustomerLookup;

    public Paging<TransferOrder> findPage(TransferOrderQuery query) {
        Paging<TransferOrder> page = transferOrderLookup.findPage(query);
        transferOrderLookup.findOperatorAssociations(page.getRecords());
        transferOrderLookup.findOwnerAssociations(page.getRecords());
        transferOrderLookup.findCountryRegionAssociations(page.getRecords());
        transferOrderLookup.findStoreAssociations(page.getRecords());
        transferOrderLookup.findSalesChannelAssociations(page.getRecords());
        transferOrderLookup.findWarehouseAssociations(page.getRecords());
        return page;
    }

    /**
     * 根据tr单状态查询待补充状态的tr单-运单号
     *
     * @param status        TS单状态,字典: transfer_order_status,可选值: 0-待补充信息 1-待拼柜 2-已拼柜 3-已作废
     * @param warehouseType 推送类型, WMS,京东,极智佳,乐鱼,KingSpark,FBA,AWD,FBT,WFS,手动签收）
     * @return 待补充状态的ts单-运单号
     */
    public List<TransferOrderData> selectTsListByStatus(Integer status, WarehouseProviderType warehouseType) {
        return transferOrderLookup.selectTsListByStatus(status, warehouseType);
    }

    /**
     * 根据TS单号列表查询TS单信息
     *
     * @param needTsNoList TS单号列表
     * @return TS单信息
     */
    public List<TransferOrderData> selectTsListByTsNoList(List<String> needTsNoList) {
        return transferOrderLookup.selectTsListByTsNoList(needTsNoList);
    }


    /**
     * 根据仓库服务商类型查询TS单信息
     *
     * @param warehouseProviderType 仓库服务商类型, 不为空
     * @param tsIds                 TS单ID集合, 可为空
     * @param tsNos                 TS单号集合, 可为空
     * @param transferOrderStatuses 状态, 不为空
     * @param syncApiStatus         同步状态, 不为空
     * @return TS单信息
     */
    public List<TransferOrder> findByWarehouseProviderType(WarehouseProviderType warehouseProviderType,
                                                           Collection<TransferOrderId> tsIds,
                                                           Collection<String> tsNos,
                                                           Collection<TransferOrderStatus> transferOrderStatuses,
                                                           String syncApiStatus) {

        SyncApiStatus apiStatus = SyncApiStatus.fromValue(syncApiStatus);
        List<TransferOrder> transferOrders = transferOrderLookup.findByWarehouseProviderType(warehouseProviderType,
                tsIds, tsNos, transferOrderStatuses, apiStatus);

        return transferOrders;
    }

    /**
     * 统计TS单各状态数量
     *
     * @return TS单各状态数量
     */
    public Map<Integer, Object> countByStatus() {
        Map<Integer, Object> map = transferOrderLookup.countByStatus();
        for (TransferOrderStatus status : TransferOrderStatus.values()) {
            if (!map.containsKey(status.getValue())) {
                map.put(status.getValue(), 0);
            }
        }
        // 为所有采购单数量之和
        final Integer ALL_STATUS_COUNT = -1;
        map.put(ALL_STATUS_COUNT, map.values().stream().map(e -> Integer.parseInt(e.toString())).reduce(0, Integer::sum));
        return map;
    }


    /**
     * 详情接口
     *
     * @param
     * <AUTHOR>
     * @date 2025/8/27 10:12
     */
    public TransferOrder detail(TransferOrderQuery query) {
        Optional<TransferOrder> orderOptional = transferOrderLookup.detail(query);
        if (orderOptional.isEmpty()) return null;

        TransferOrder order = orderOptional.get();
        transferOrderLookup.findOperatorAssociations(List.of(order));
        transferOrderLookup.findOwnerAssociations(List.of(order));
        transferOrderLookup.findCountryRegionAssociations(List.of(order));
        transferOrderLookup.findStoreAssociations(List.of(order));
        transferOrderLookup.findSalesChannelAssociations(List.of(order));
        transferOrderLookup.findWarehouseAssociations(List.of(order));

        //客户信息
        Optional<TransferOrderCustomer> customerOptional = transferOrderCustomerLookup.findByTsId(order.getId());
        customerOptional.ifPresent(order::setCustomer);

        //todo 物流信息


        //关联批注信息
        List<TransferOrderComment> comments = transferOrderCommentLookup.findListByToId(order.getId());
        order.setComments(comments);
        //关联文件信息
        List<OrderFile> files = orderFileLookup.findListByOrderId(order.getId().id(), BusinessTypeEnum.TS, null);
        order.setFiles(files);
        //关联状态历史信息
        boolean isVC = order.getTsBizType() == TransferOrderBizType.B2B_CUSTOMER || order.getTsBizType() == TransferOrderBizType.VC_DO;
        List<TransferOrderStatusHistory> statusHistoryList =
                transferOrderStatusHistoryLookup.findTransferOrderStatusFlow(order.getId(), isVC);
        order.setStatusHistoryList(statusHistoryList);

        return order;
    }

    /**
     * 根据ts单状态跟仓库类型，查询item明细带有出库单编码的数据
     *
     * @param status        TS单状态
     * @param warehouseType 仓库类型
     * @return TS主表对应的item明细带有出库单编码的数据
     */
    public List<TransferOrderData> selectTsListWithItemOutboundNo(TransferOrderStatus status, WarehouseProviderType warehouseType) {
        return transferOrderLookup.selectTsListWithItemOutboundNo(status, warehouseType);
    }
}
