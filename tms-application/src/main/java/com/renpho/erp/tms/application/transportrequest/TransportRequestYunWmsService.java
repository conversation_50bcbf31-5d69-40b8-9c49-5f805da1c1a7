package com.renpho.erp.tms.application.transportrequest;

import com.renpho.erp.apiproxy.eccang.yunwms.model.instock.GetAsnListResponse;
import com.renpho.erp.tms.application.api.yunwms.YunWmsInboundOrderContext;
import com.renpho.erp.tms.application.api.yunwms.strategy.TransportRequestYunWmsInboundStrategy;
import com.renpho.erp.tms.application.inbound.InboundAllocationProducer;
import com.renpho.erp.tms.application.inbound.InboundRecordQueryService;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transportorder.TransportOrderService;
import com.renpho.erp.tms.domain.inbound.*;
import com.renpho.erp.tms.domain.saleschannel.SalesChannelConstant;
import com.renpho.erp.tms.domain.transportorder.TransportOrderStatusEnum;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.erp.tms.domain.transportrequest.TransportRequest;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestId;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestStatus;
import com.renpho.erp.tms.infrastructure.common.constant.YunWmsConstant;
import com.renpho.erp.tms.infrastructure.persistence.inbound.po.converter.InboundRecordConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderItemRepository;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderRepository;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.inbound.EccangInboundClient;
import com.renpho.erp.tms.infrastructure.remote.warehouse.repository.WarehouseLookup;
import com.renpho.erp.tms.infrastructure.util.DateUtils;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/7/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransportRequestYunWmsService {
    private final TransportRequestQueryService transportRequestQueryService;
    private final TransportRequestOrderRepository transportRequestOrderRepository;
    private final TransportRequestService transportRequestService;
    private final TransportRequestOrderItemRepository transportRequestOrderItemRepository;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final PushTaskService pushTaskService;
    private final EccangInboundClient eccangInboundClient;
    private final WarehouseLookup warehouseLookup;
    private final InboundRecordRepository inboundRecordRepository;
    private final InboundRecordQueryService inboundRecordQueryService;
    private final InboundRecordConverter inboundRecordConverter;
    private final InboundAllocationProducer inboundAllocationProducer;
    private final TransportOrderService transportOrderService;
    private final YunWmsInboundOrderContext yunWmsInboundOrderContext;
    private final TransportRequestYunWmsInboundStrategy transportRequestStrategy;

    /**
     * 创建入库单
     *
     * @param trNos         TR单号
     * @param trIds         TR单ID
     * @param isAmazon      true-amazon平台TR单 false-非amazon平台TR单
     * @param syncApiStatus 同步状态
     */
    public void creatAsn(List<String> trNos, List<TransportRequestId> trIds, Boolean isAmazon, String syncApiStatus) {

        //“待补充信息”状态TR单
        List<TransportRequest> trList =
                transportRequestQueryService.findByWarehouseProviderType(WarehouseProviderType.POLARIS_YUNWMS, trIds,
                                trNos, null, TransportRequestStatus.PENDING, syncApiStatus)
                        .stream().filter(tr -> StringUtil.isEmpty(tr.getShipmentId())).toList();

        if (isAmazon) {
            //amazon tr单，走中转逻辑
            List<TransportRequest> amzTrs = trList.stream()
                    .filter(tr -> StringUtil.isEmpty(tr.getShipmentId())
                            && SalesChannelConstant.AMAZON.equalsIgnoreCase(tr.getSalesChannel().getChannelName()))
                    .toList();
            //创建 转运单（中转，amazon平台TR）
            yunWmsInboundOrderContext.executeCreateTransferInbound(amzTrs, transportRequestStrategy, TransportRequest::getDestWarehouse);
        } else {
            //非Amazon tr单，走一件代发逻辑
            List<TransportRequest> nonAmzTrs = trList.stream()
                    .filter(tr -> StringUtil.isEmpty(tr.getShipmentId())
                            && !SalesChannelConstant.AMAZON.equalsIgnoreCase(tr.getSalesChannel().getChannelName()))
                    .toList();
            //创建 ASN（一件代发，非amazon平台TR）
            yunWmsInboundOrderContext.executeCreateAsn(nonAmzTrs, transportRequestStrategy, TransportRequest::getDestWarehouse);
        }
    }


    /**
     * 中转-获取箱唛
     *
     * @param trNos TR单号
     * @param trIds TR单ID
     */
    public void syncTransferOrderBoxLabel(List<String> trNos, List<TransportRequestId> trIds, String syncApiStatus) {
        //“待补充信息”状态TR单
        List<TransportRequest> trList =
                transportRequestQueryService.findByWarehouseProviderType(WarehouseProviderType.POLARIS_YUNWMS, trIds,
                        trNos, null, TransportRequestStatus.PENDING, syncApiStatus);

        List<TransportRequest> trs = trList.stream()
                .filter(tr -> SalesChannelConstant.AMAZON.equalsIgnoreCase(tr.getSalesChannel().getChannelName())
                        && StringUtil.isNotBlank(tr.getShipmentId())).toList();

        yunWmsInboundOrderContext.executeGetTransferOrderBoxLabel(trs, transportRequestStrategy, TransportRequest::getDestWarehouse);
    }


    /**
     * 作废转运单
     *
     * @param tr 传TR单的shipmentID
     */
    public void cancelTransferOrder(TransportRequest tr) {
        yunWmsInboundOrderContext.executeCancelTransferOrder(List.of(tr), transportRequestStrategy, TransportRequest::getDestWarehouse);
    }

    public void cancelAsn(TransportRequest tr) {
        yunWmsInboundOrderContext.executeCancelAsn(List.of(tr), transportRequestStrategy, TransportRequest::getDestWarehouse);
    }

    public void cancelAsn(List<String> trNos, List<TransportRequestId> trIds, String syncApiStatus) {

        //“待补充信息”状态TR单
        List<TransportRequest> trList =
                transportRequestQueryService.findByWarehouseProviderType(WarehouseProviderType.POLARIS_YUNWMS, trIds,
                        trNos, null, TransportRequestStatus.CANCEL, syncApiStatus);

        List<TransportRequest> trs = trList.stream()
                .filter(tr -> !SalesChannelConstant.AMAZON.equalsIgnoreCase(tr.getSalesChannel().getChannelName())
                        && StringUtil.isNotBlank(tr.getShipmentId())).toList();
        
        // 使用通用的executeCancelAsn方法
        yunWmsInboundOrderContext.executeCancelAsn(trs, transportRequestStrategy, TransportRequest::getDestWarehouse);
    }


    /**
     * 极智佳-中转-拉取转运单（Amazon平台）
     *
     * @param trNos TR单号
     * @param trIds TR单ID
     */
    public void pullTransferOrder(List<String> trNos, List<TransportRequestId> trIds, String syncApiStatus) {
        //获取所有状态为已派送或部分签收的极智佳 TR单
        List<TransportRequest> trList =
                transportRequestQueryService.findByWarehouseProviderType(WarehouseProviderType.POLARIS_YUNWMS, trIds, trNos,
                        Arrays.asList(TransportOrderStatusEnum.OUT_FOR_DELIVERY,
                                TransportOrderStatusEnum.DELIVERED_PART), null, syncApiStatus);

        List<TransportRequest> trs = trList.stream()
                .filter(tr -> SalesChannelConstant.AMAZON.equalsIgnoreCase(tr.getSalesChannel().getChannelName())
                        && StringUtil.isNotBlank(tr.getShipmentId())).toList();

        yunWmsInboundOrderContext.executePullTransferOrder(trs, transportRequestStrategy, TransportRequest::getDestWarehouse);
    }


    /**
     * 极智佳-中转-作废转运单（Amazon平台）
     *
     * @param trNos TR单号
     * @param trIds TR单ID
     */
    public void cancelTransferOrder(List<String> trNos, List<TransportRequestId> trIds, String syncApiStatus) {

        //“待补充信息”状态TR单
        List<TransportRequest> trList =
                transportRequestQueryService.findByWarehouseProviderType(WarehouseProviderType.POLARIS_YUNWMS, trIds,
                        trNos, null, TransportRequestStatus.CANCEL, syncApiStatus);

        List<TransportRequest> trs = trList.stream()
                .filter(tr -> SalesChannelConstant.AMAZON.equalsIgnoreCase(tr.getSalesChannel().getChannelName())
                        && StringUtil.isNotBlank(tr.getShipmentId())).toList();

        yunWmsInboundOrderContext.executeCancelTransferOrder(trs, transportRequestStrategy, TransportRequest::getDestWarehouse);
    }


    /**
     * 一件代发-获取箱唛文件
     *
     * @param trNos TR单号
     * @param trIds TR单ID
     */
    public void syncYplBoxLabel(List<String> trNos, List<TransportRequestId> trIds, String syncApiStatus) {
        //“待补充信息”状态TR单
        List<TransportRequest> trList =
                transportRequestQueryService.findByWarehouseProviderType(WarehouseProviderType.POLARIS_YUNWMS, trIds,
                        trNos, null, TransportRequestStatus.PENDING, syncApiStatus);


        List<TransportRequest> trs = trList.stream()
                .filter(tr -> !SalesChannelConstant.AMAZON.equalsIgnoreCase(tr.getSalesChannel().getChannelName())
                        && StringUtil.isNotBlank(tr.getShipmentId())).toList();

        // 使用通用的executeGetAsnBoxLabel方法
        yunWmsInboundOrderContext.executeGetAsnBoxLabel(trs, transportRequestStrategy, TransportRequest::getDestWarehouse);
    }




    /**
     * 极智佳-获取ASN列表
     *
     * @param trNos TR单号
     * @param trIds TR单ID
     */
    public void pullAsnList(List<String> trNos, List<TransportRequestId> trIds, String syncApiStatus) {
        //获取所有状态为已派送或部分签收的极智佳 TR单
        List<TransportRequest> trList =
                transportRequestQueryService.findByWarehouseProviderType(WarehouseProviderType.POLARIS_YUNWMS, trIds, trNos,
                        Arrays.asList(TransportOrderStatusEnum.OUT_FOR_DELIVERY,
                                TransportOrderStatusEnum.DELIVERED_PART), null, syncApiStatus);

        List<TransportRequest> trs = trList.stream()
                .filter(tr -> !SalesChannelConstant.AMAZON.equalsIgnoreCase(tr.getSalesChannel().getChannelName())
                        && StringUtil.isNotBlank(tr.getShipmentId())).toList();

        // 使用通用的executePullAsnList方法
        yunWmsInboundOrderContext.executePullAsnList(trs, transportRequestStrategy, TransportRequest::getDestWarehouse);
    }


    /**
     * 处理入库单列表数据
     *
     * @param dataList  入库单列表数据
     * @param tr        tr单
     * @param historyId 入库请求记录ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleAsnListData(List<GetAsnListResponse.AsnListData> dataList, TransportRequest tr, InboundRequestHistoryId historyId) {
        dataList.forEach(asn -> {
            String receivingStatus = asn.getReceiving_status();

            InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tr).orElse(null);


            //签收数量
            int receivedQty = asn.getItems().stream().map(GetAsnListResponse.Item::getReceived_quantity).mapToInt(Integer::parseInt).sum();
            int putawayQty = asn.getItems().stream().mapToInt(GetAsnListResponse.Item::getPutaway_quantity).sum();

            InboundRecord record = inboundRecordConverter.toDomain(tr);
            //是否首次收货
            boolean isFirstReceived = tr.checkTrIsFirstReceived();

            boolean haveNewReceived = false, haveNewPutaway = false;

            //收货完成或者上架完成，则保存TR单的签收时间、签收数量、上架时间、上架数量
            if (Set.of(YunWmsConstant.InboundOrderStatus.RECEIVE_COMPLETED_DEST,
                    YunWmsConstant.InboundOrderStatus.COMPLETED_PUTAWAY).contains(receivingStatus)) {
                record.setWarehouseType(WarehouseProviderType.POLARIS_YUNWMS);
                record.setRequestHistoryId(historyId);
                record.setStatus(InboundStatus.WORKING);
                record.setPutawayStatus(InboundStatus.WORKING);
                tr.setShipStatus(TransportOrderStatusEnum.DELIVERED_PART);

                haveNewReceived = record.buildReceivedInfo(receivedQty, lastRecord);
                haveNewPutaway = record.buildPutawayInfo(putawayQty, lastRecord);

                //签收时间
                String warehouseReceivingCompleteTime = asn.getWarehouse_receiving_complete_time();
                if (StringUtil.isNotBlank(warehouseReceivingCompleteTime)) {
                    LocalDateTime receivedTime = DateUtils.toUtcLocalDateTime(warehouseReceivingCompleteTime);
                    record.setReceivedTime(receivedTime);
                    if (isFirstReceived) {
                        //设置TR单的开始签收时间
                        tr.setReceivedTime(receivedTime);
                    }
                }
                //上架时间
                String warehouseShelfTime = asn.getWarehouse_shelf_time();
                if (StringUtil.isNotBlank(warehouseShelfTime)) {
                    LocalDateTime shelfTime = DateUtils.toUtcLocalDateTime(warehouseShelfTime);
                    record.setPutawayTime(shelfTime);
                }

                //记录TR单的实际签收数量、上架数量
                tr.getItem().setReceivedQuantity(record.getReceivedQuantityTotal());
                tr.getItem().setShelvedQuantity(record.getPutawayQuantityTotal());

                //入库单状态上架完成了的，则TR单出运状态更新“签收完成”；
                if (YunWmsConstant.InboundOrderStatus.COMPLETED_PUTAWAY.equals(receivingStatus)) {
                    //更新TR单出运状态更新“已签收”
                    tr.setShipStatus(TransportOrderStatusEnum.DELIVERED);
                    tr.setReceivedEndTime(record.getReceivedTime());
                }
            }


            //存在增量数据
            if (haveNewReceived || haveNewPutaway) {
                //更新TR单
                tr.setSyncApiStatus(SyncApiStatus.SUCCESS);
                transportRequestOrderRepository.updateById(tr);
                transportRequestOrderItemRepository.update(tr);
                // 判断此TR单对应的TO单下所有TR是否全部都是已签收状态，如果是，则将TO状态改为已签收
                transportOrderService.syncTransportOrderStatus(tr, TransportOrderStatusEnum.DELIVERED);

                //入库单状态上架完成了的，则TR单出运状态更新“签收完成”；
                if (YunWmsConstant.InboundOrderStatus.COMPLETED_PUTAWAY.equals(receivingStatus)) {
                    record.setStatus(InboundStatus.FINISH);
                    record.setPutawayStatus(InboundStatus.FINISH);

                    //签收完成后，计算签收、上架差异数量
                    boolean haveDiscrepancy = tr.getItem().calculateTheDifferenceValue(record.getReceivedQuantityTotal(), record.getPutawayQuantityTotal());
                    transportRequestOrderItemRepository.update(tr);

                    if (haveDiscrepancy) {
                        //todo 调用IMS 接口，创建差异单
//                        DiscrepancyOrder discrepancyOrder =
                    } else {
                        //差异值处理完后，若无差异，更新TR单出运状态更新“已完成"
                        tr.setShipStatus(TransportOrderStatusEnum.COMPLETED);
                        //更新TR单
                        transportRequestOrderRepository.updateById(tr);
                        transportOrderService.syncTransportOrderStatus(tr, TransportOrderStatusEnum.COMPLETED);
                    }
                }

                //保存签收、上架数据
                record = inboundRecordRepository.add(record);
                inboundAllocationProducer.send(record);

                //签收推送签收消息到FMS
                transportRequestService.firstReceiveSendReceiveMsgToFms(tr.getToNo(), isFirstReceived);
            }
        });
    }
}
