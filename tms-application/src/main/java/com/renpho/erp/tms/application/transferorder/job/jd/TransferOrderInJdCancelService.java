package com.renpho.erp.tms.application.transferorder.job.jd;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transferorder.TransferOrderService;
import com.renpho.erp.tms.application.transferorder.job.common.TransferOrderCommonService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.domain.exception.DingTalkWarning;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transferorder.TransferOrder;
import com.renpho.erp.tms.domain.transferorder.TransferOrderData;
import com.renpho.erp.tms.domain.transferorder.TransferOrderRepository;
import com.renpho.erp.tms.domain.transferorder.TransferOrderStatus;
import com.renpho.erp.tms.domain.transportrequest.PushTask;
import com.renpho.erp.tms.domain.transportrequest.PushTaskStatus;
import com.renpho.erp.tms.domain.transportrequest.dto.WarehouseData;
import com.renpho.erp.tms.infrastructure.common.oplog.LogModule;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderConverter;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.repository.TransferOrderItemRepository;

import com.renpho.erp.tms.infrastructure.remote.jd.dto.cancel.CancelServiceBillDto;
import com.renpho.erp.tms.infrastructure.remote.jd.dto.cancel.JdCancelResponseVo;
import com.renpho.erp.tms.infrastructure.remote.jd.feign.JdFeign;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * TS-目的仓为Jd-取消入库单任务.
 *
 * <AUTHOR>
 * @since 2025/8/26
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransferOrderInJdCancelService {

    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransferOrderCommonService transferOrderCommonService;
    private final TransferOrderService transferOrderService;
    private final PushTaskService pushTaskService;

    private final TransferOrderRepository transferOrderRepository;
    private final TransferOrderItemRepository transferOrderItemRepository;
    private final TransferOrderConverter transferOrderConverter;

    private final JdFeign jdFeign;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.JD;

    /**
     * TR-目的仓为京东-取消入库单任务.
     */
    @Lock4j(name = "transfer:request:jd:inbound:cancel:basic")
    //@Transactional(rollbackFor = Exception.class)
    @LogRecord(module = LogModule.TRANSPORT_REQUEST, type = LogModule.CommonDesc.CANCEL_OPERATOR, desc =
            LogModule.CommonDesc.CANCEL_DESC)
    public void createInboundJdTask(List<String> tsNoList) {
        try {
            log.info("TS-目的仓为Jd的定时器任务开始");

            // 状态是已作废
            TransferOrderStatus status = TransferOrderStatus.CANCEL;
            List<TransferOrderData> tsDataList = transferOrderCommonService.selectTsListByStatus(status.getValue(), warehouseType, true);

            // 外部参数
            tsDataList = transferOrderCommonService.fillExterWithTsStatus(status, tsDataList, tsNoList);

            // 生成入库单推送任务
            if (tsDataList != null && !tsDataList.isEmpty()) {
                // 获取tr上下文信息
                Map<Integer, TransferOrder> tsMap = transferOrderService.getTsMapByIds(tsDataList.stream().map(TransferOrderData::getId).toList());

                for (TransferOrderData tsData : tsDataList) {
                    try {
                        TransferOrder ts = tsMap.get(tsData.getId());
                        if (ts == null) {
                            log.error("TS-目的仓为Jd-取消入库单任务生成异常, ts上下文找不到, tsId={}", tsData.getId());
                            continue;
                        }
                        // 目的仓限制
                        if (warehouseType != ts.findWarehouseProviderType()) {
                            continue;
                        }

                        // 生成推送任务
                        PushTask trPushTask = pushTaskService.cancel(ts, PushTaskStatus.PENDING, 0, null);

                        // 执行取消
                        TransferOrder oldData = pushTaskService.execute(() -> this.cancel(ts, trPushTask), trPushTask);

                        // 记录日志
                        if (oldData != null) {
                            LogRecordContextHolder.putRecordData(String.valueOf(ts.getId().id()), oldData, ts);
                        }
                    } catch (Exception e) {
                        log.error("TS-目的仓为Jd的定时器任务异常, 入库单推送任务生成失败, tsId={}", tsData.getId(), e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("TS-目的仓为Jd的定时器任务异常", e);
        }
    }

    private TransferOrder cancel(TransferOrder ts, PushTask tsPushTask) {
        WarehouseData warehouseData = transferOrderCommonService.getJDWarehouse(ts.getDestWarehouse().getId().id());
        if (warehouseData == null) {
            log.error("TS-目的仓为JD的定时器任务异常, 无法获取客户编码, tsId={}, destWarehouseId={}", ts.getId(), ts.getDestWarehouse().getId().id());
            return null;
        }

        CancelServiceBillDto cancelVo = new CancelServiceBillDto();
        cancelVo.setCustomerCode(warehouseData.getCustomerCode());
        cancelVo.setGatewayUser(warehouseData.getAccount());
        cancelVo.setCustomerBillCode(ts.getTsNo());
        cancelVo.setOperator(warehouseData.getOwnerCode());
        cancelVo.setSourceSystem("TMS");

        TransferOrder oldData = transferOrderConverter.tsToCopy(ts);
        log.info("TS-目的仓为Jd的取消任务,请求参数={}", JSON.toJSONString(cancelVo));
        JdCancelResponseVo result = jdFeign.cancelServiceBill(cancelVo);
        log.info("TS-目的仓为Jd的取消任务,响应参数={}", JSON.toJSONString(result));
        if (result != null) {
            if (result.getResult().isSuccess()) {
                // 记录请求历史
                inboundRequestHistoryService.add(ts, WarehouseProviderType.JD, null, cancelVo, 200, null, result);

                // 清除ShipmentId信息,并更新状态
                transferOrderRepository.clearShipmentIdById(ts.getId());
            } else {
                // 记录请求历史
                inboundRequestHistoryService.add(ts, WarehouseProviderType.JD, null, cancelVo, 200, null, result);
                throw new DingTalkWarning("TS取消入库单失败，原因：%s".formatted(result.getResult().getErrorMsg()));
            }
        }
        return oldData;
    }

}
