package com.renpho.erp.tms.adapter.web.controller.logisticsmode.vo;

import com.fhs.core.trans.vo.VO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 物流方式字典表 VO
 *
 * <AUTHOR>
 * @since 2025/8/28
 */
@Getter
@Setter
public class LogisticsModeVO implements Serializable, VO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 物流服务代码
     */
    private String code;

    /**
     * 服务商的物流方式名称中文
     */
    private String nameCn;

    /**
     * 服务商的物流方式名称英文
     */
    private String nameEn;

    /**
     * 订单类型
     */
    private String type;

    /**
     * 可用仓库id（做过滤）
     */
    private Integer availableWarehouseId;

    /**
     * 可用仓库code（做过滤）
     */
    private String availableWarehouseCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态, 暂未使用
     */
    private Integer status;

    /**
     * 是否删除, 0-未删除, 1-删除
     */
    private Integer isDeleted;

    /**
     * 创建人ID
     */
    private Integer createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Integer updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
