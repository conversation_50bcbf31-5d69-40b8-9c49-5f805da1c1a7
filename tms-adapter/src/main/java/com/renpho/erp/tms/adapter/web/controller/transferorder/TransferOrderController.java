package com.renpho.erp.tms.adapter.web.controller.transferorder;

import com.fhs.core.trans.anno.TransMethodResult;
import com.renpho.erp.tms.adapter.web.controller.command.validator.*;
import com.renpho.erp.tms.adapter.web.controller.transferorder.cmd.AddCmd;
import com.renpho.erp.tms.adapter.web.controller.transferorder.cmd.TransferOrderDetailCmd;
import com.renpho.erp.tms.adapter.web.controller.transferorder.cmd.UpdateCmd;
import com.renpho.erp.tms.adapter.web.controller.transferorder.cmd.converter.TransferOrderCmdConverter;
import com.renpho.erp.tms.adapter.web.controller.transferorder.vo.TransferOrderDetailVO;
import com.renpho.erp.tms.adapter.web.controller.transferorder.vo.TransferOrderVO;
import com.renpho.erp.tms.adapter.web.controller.transferorder.vo.converter.TransferOrderVoConverter;
import com.renpho.erp.tms.application.transferorder.TransferOrderQueryService;
import com.renpho.erp.tms.application.transferorder.TransferOrderService;
import com.renpho.erp.tms.application.transportrequest.PushTaskQueryService;
import com.renpho.erp.tms.domain.exception.BizErrorCode;
import com.renpho.erp.tms.domain.transferorder.TransferOrder;
import com.renpho.erp.tms.domain.transferorder.TransferOrderQuery;
import com.renpho.erp.tms.domain.transportrequest.PushTask;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 调拨单接口.
 *
 * <AUTHOR>
 * @since 2025/8/23
 */
@Validated
@RestController
@RequestMapping("/transfer/order")
@ShenyuSpringCloudClient("/transfer/order/**")
@RequiredArgsConstructor
public class TransferOrderController {

    private final TransferOrderCmdConverter transferOrderCmdConverter;
    private final TransferOrderService transferOrderService;
    private final TransferOrderVoConverter transferOrderVoConverter;
    private final TransferOrderQueryService transferOrderQueryService;
    private final PushTaskQueryService pushTaskQueryService;


    @PostMapping("/page")
    @TransMethodResult
    @PreAuthorize("hasPermission('tms:transferOrder:list')")
    public R<Paging<TransferOrderVO>> findPage(@RequestBody TransferOrderQuery query) {

        Paging<TransferOrder> page = transferOrderQueryService.findPage(query);
        //查询推送异常
        Map<String, List<String>> failures = pushTaskQueryService.findFailuresByBizNos(page.getRecords())
                .entrySet()
                .stream()
                .filter(Objects::nonNull)
                .map(e -> Map.entry(e.getKey(), e.getValue().stream()
                        .filter(Objects::nonNull).map(PushTask::getErrMsg)
                        .filter(StringUtils::isNotBlank).toList()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        Paging<TransferOrderVO> vos = transferOrderVoConverter.toPageVos(page);

        vos.getRecords().forEach(e ->
                Optional.ofNullable(failures.get(e.getTsNo()))
                        .ifPresent(e::setLastErrMsg)
        );
        return R.success(vos);
    }

    /**
     * 统计TS单各状态数量
     */
    @PreAuthorize("hasPermission('tms:transferOrder:list')")
    @PostMapping("/countByStatus")
    public R<Map<Integer, Object>> countByStatus() {
        return R.success(transferOrderQueryService.countByStatus());
    }

    /**
     * 创建调拨单
     *
     * <AUTHOR>
     * @since 2025/8/23
     */
    @PostMapping("/add")
    public R<TransferOrderVO> add(@Validated @RequestBody @StoreExist @StoresExist @WarehousesExist @CountryRegionExist @ItemsExist AddCmd cmd) {
        TransferOrder command = transferOrderCmdConverter.toDomain(cmd);
        TransferOrder domain = transferOrderService.add(command);
        // TODO 日志
        return R.success(transferOrderVoConverter.toVo(domain));
    }

    /**
     * 编辑调拨单
     *
     * <AUTHOR>
     * @since 2025/8/23
     */
    @PostMapping("/update")
    public R<TransferOrderVO> update(@Validated @RequestBody @StoreExist @StoresExist @WarehousesExist @CountryRegionExist @ItemsExist UpdateCmd cmd) {
        TransferOrder command = transferOrderCmdConverter.toDomain(cmd);
        TransferOrder domain = transferOrderService.update(command);
        // TODO 日志
        return R.success(transferOrderVoConverter.toVo(domain));
    }



    /**
     * 详情
     *
     * @param cmd 参数
     * @return 详情
     */
    @PostMapping("/detail")
    @TransMethodResult
    @PreAuthorize("hasPermission('tms:transferOrder:detail')")
    public R<TransferOrderDetailVO> detail(@RequestBody TransferOrderDetailCmd cmd) {
        Assert.state(Objects.isNull(cmd.getToId()) || StringUtils.isBlank(cmd.getTsNo()),
                BizErrorCode.INVALID_REQUEST_CONTENT.getMessage());

        TransferOrderQuery query = transferOrderCmdConverter.toDetailDomain(cmd);
        TransferOrder order = transferOrderQueryService.detail(query);
        TransferOrderDetailVO vo = transferOrderVoConverter.toDetailVo(order);
        return R.success(vo);
    }

}
