package com.renpho.erp.tms.adapter.web.controller.transferorder.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.tms.adapter.web.controller.orderfile.vo.OrderFileVO;
import com.renpho.erp.tms.adapter.web.controller.transportorder.vo.TransportOrderCommentVO;
import com.renpho.erp.tms.adapter.web.controller.transportorder.vo.TransportOrderStatusHistoryVO;
import com.renpho.erp.tms.domain.transferorder.TransferOrderDataSource;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/8/27
 */
@Getter
@Setter
public class TransferOrderDetailVO extends TransferOrderVO implements Serializable, VO {

    /**
     * 文件信息
     */
    private List<OrderFileVO> files;

    /**
     * 批注列表
     */
    private List<TransportOrderCommentVO> comments;

    /**
     * 状态历史列表
     */
    private List<TransportOrderStatusHistoryVO> statusChangeTimeRecords;
}
