package com.renpho.erp.tms.adapter.web.controller.transferorder.vo.converter;

import com.renpho.erp.tms.adapter.web.controller.orderfile.vo.converter.OrderFileVOConverter;
import com.renpho.erp.tms.adapter.web.controller.transferorder.vo.TransferOrderDetailVO;
import com.renpho.erp.tms.adapter.web.controller.transferorder.vo.TransferOrderVO;
import com.renpho.erp.tms.domain.transferorder.TransferOrder;
import com.renpho.erp.tms.domain.transferorder.TransferOrderCommentId;
import com.renpho.erp.tms.domain.transferorder.TransferOrderStatusHistoryId;
import com.renpho.erp.tms.infrastructure.common.converter.MultiLanguageConverter;
import com.renpho.erp.tms.infrastructure.common.converter.TransferOrderStatusConverter;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderConverter;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderItemConverter;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderStatusHistoryConverter;
import com.renpho.karma.dto.Paging;
import org.mapstruct.*;

import java.util.Collection;
import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {TransferOrderItemVoConverter.class,
                MultiLanguageConverter.class,
                TransferOrderConverter.class,
                TransferOrderItemConverter.class,
                OrderFileVOConverter.class,
                TransferOrderCommentVOConverter.class,
                TransferOrderStatusConverter.class,
                TransferOrderStatusHistoryConverter.class})
public interface TransferOrderVoConverter {

    @Named("toTransferOrderVO")
    @Mapping(target = "id", source = "id.id")
    @Mapping(target = "salesChannelId", source = "salesChannel.id.id")
    @Mapping(target = "salesChannelName", source = "salesChannel.channelName")
    @Mapping(target = "ownerId", source = "owner.id.id")
    @Mapping(target = "ownerName", source = "owner.names")
    @Mapping(target = "shippingWarehouse", source = "shippingWarehouse.name")
    @Mapping(target = "shippingWarehouseId", source = "shippingWarehouse.id.id")
    @Mapping(target = "destCountryCode", source = "destCountry.code")
    @Mapping(target = "destWarehouseId", source = "destWarehouse.id.id")
    @Mapping(target = "destWarehouseCode", source = "destWarehouseCode")
    @Mapping(target = "destWarehouseName", source = "destWarehouse.name")
    @Mapping(target = "createBy", source = "created.operatorName")
    @Mapping(target = "createTime", source = "created.operateTime")
    @Mapping(target = "updateBy", source = "updated.operatorName")
    @Mapping(target = "updateTime", source = "updated.operateTime")
    @Mapping(target = "storeId", source = "store.id.id")
    @Mapping(target = "status", source = "status.value")
    @Mapping(target = "salesStaffId", source = "salesStaffId.id")
    @Mapping(target = "planerStaffId", source = "planerStaffId.id")
    @Mapping(target = "shippingStaffId", source = "shippingStaffId.id")
    @Mapping(target = "customer.id", source = "customer.id.id")
    TransferOrderVO toVo(TransferOrder transferOrder);

    @IterableMapping(qualifiedByName = "toTransferOrderVO", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<TransferOrderVO> toVos(Collection<TransferOrder> transferOrder);

    Paging<TransferOrderVO> toPageVos(Paging<TransferOrder> page);

    @InheritConfiguration(name = "toVo")
    @Mapping(target = "id", source = "id.id")
    @Mapping(target = "files", source = "files", qualifiedByName = "groupByFileType")
    @Mapping(target = "comments", source = "comments")
    @Mapping(target = "statusChangeTimeRecords", source = "statusHistoryList")
    TransferOrderDetailVO toDetailVo(TransferOrder domain);

    default Integer map(TransferOrderCommentId value) {
        return value == null ? null : value.id(); 
    }

    default Integer map(TransferOrderStatusHistoryId value) {
        return value == null ? null : value.id();
    }
}
