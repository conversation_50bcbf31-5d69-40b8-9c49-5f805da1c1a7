package com.renpho.erp.tms.adapter.web.controller.transferorder.vo.converter;

import com.renpho.erp.tms.adapter.web.controller.transferorder.vo.TransferOrderItemVO;
import com.renpho.erp.tms.domain.transferorder.TransferOrderItem;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderConverter;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderItemConverter;
import com.renpho.erp.tms.infrastructure.remote.owner.converter.OwnerConverter;
import com.renpho.erp.tms.infrastructure.remote.saleschannel.converter.SalesChannelConverter;
import com.renpho.erp.tms.infrastructure.remote.store.converter.StoreConverter;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import org.mapstruct.*;

import java.util.Collection;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/8/25
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {TransferOrderConverter.class, TransferOrderItemConverter.class, SalesChannelConverter.class, StoreConverter.class, OwnerConverter.class, OperatorConverter.class})
public interface TransferOrderItemVoConverter {

    @Mapping(target = "id", source = "id.id")
    @Mapping(target = "tsId", ignore = true)
    TransferOrderItemVO toVo(TransferOrderItem transferOrderItem);

    List<TransferOrderItemVO> toVos(Collection<TransferOrderItem> transferOrderItems);

}
