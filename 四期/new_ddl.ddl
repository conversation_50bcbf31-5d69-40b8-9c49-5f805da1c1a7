CREATE TABLE `tms_logistics_mode`
(
    `id`                       int          NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `code`                     varchar(64)  NOT NULL COMMENT '物流服务代码',
    `name_cn`                  varchar(255) NOT NULL COMMENT '服务商的物流方式名称中文',
    `name_en`                  varchar(255) NOT NULL COMMENT '服务商的物流方式名称英文',
    `type`                     char(64)     NOT NULL COMMENT '订单类型',
    `available_warehouse_id`   int                   DEFAULT NULL COMMENT '可用仓库id（做过滤）',
    `available_warehouse_code` varchar(255) NOT NULL COMMENT '可用仓库code（做过滤）',
    `remark`                   text COMMENT '备注',
    `status`                   int          NOT NULL DEFAULT '1' COMMENT '状态, 暂未使用',
    `is_deleted`               tinyint      NOT NULL DEFAULT '0' COMMENT '是否删除, 0-未删除, 1-删除',
    `create_by`                int          NOT NULL COMMENT '创建人ID',
    `create_time`              datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`                int          NOT NULL COMMENT '更新人ID',
    `update_time`              datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_code` (`code`),
    KEY `idx_name` (`name_cn`)
) COMMENT ='物流方式字典表';