package com.renpho.erp.tms.infrastructure.persistence.logisticsmode.po.converter;

import com.renpho.erp.tms.domain.logisticsmode.LogisticsMode;
import com.renpho.erp.tms.domain.logisticsmode.LogisticsModeId;
import com.renpho.erp.tms.infrastructure.persistence.logisticsmode.po.LogisticsModePO;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import com.renpho.erp.tms.infrastructure.remote.warehouse.converter.WarehouseConverter;
import org.mapstruct.*;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 物流方式转换器
 *
 * <AUTHOR>
 * @since 2025/8/28
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {OperatorConverter.class, WarehouseConverter.class})
public interface LogisticsModeConverter {

    @Mapping(target = "id", source = "id")
    @Mapping(target = "availableWarehouseId", source = "availableWarehouseId")
    @Mapping(target = "availableWarehouseCode", source = "availableWarehouseCode")
    @Mapping(target = "availableWarehouse.id", source = "availableWarehouseId")
    @Mapping(target = "availableWarehouse.code", source = "availableWarehouseCode")
    @Mapping(target = "created.operatorId", source = "createBy")
    @Mapping(target = "created.operateTime", source = "createTime")
    @Mapping(target = "updated.operatorId", source = "updateBy")
    @Mapping(target = "updated.operateTime", source = "updateTime")
    LogisticsMode toDomain(LogisticsModePO po);

    @InheritInverseConfiguration
    @Mapping(target = "availableWarehouseId", source = "availableWarehouseId")
    @Mapping(target = "availableWarehouseCode", source = "availableWarehouseCode")
    LogisticsModePO toPo(LogisticsMode domain);

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<LogisticsMode> toDomains(Collection<LogisticsModePO> pos);

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<LogisticsModePO> toPos(Collection<LogisticsMode> domains);

    @Mapping(target = "id", source = "id")
    LogisticsModeId toId(Integer id);

    default Integer toId(LogisticsModeId id) {
        return Optional.ofNullable(id).map(LogisticsModeId::id).orElse(null);
    }

}
