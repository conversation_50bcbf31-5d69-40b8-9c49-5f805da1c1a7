package com.renpho.erp.tms.infrastructure.persistence.logisticsmode.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.tms.domain.logisticsmode.LogisticsMode;
import com.renpho.erp.tms.domain.logisticsmode.LogisticsModeId;
import com.renpho.erp.tms.domain.logisticsmode.LogisticsModeLookup;
import com.renpho.erp.tms.domain.warehouse.WarehouseId;
import com.renpho.erp.tms.infrastructure.persistence.logisticsmode.mapper.LogisticsModeMapper;
import com.renpho.erp.tms.infrastructure.persistence.logisticsmode.po.LogisticsModePO;
import com.renpho.erp.tms.infrastructure.persistence.logisticsmode.po.converter.LogisticsModeConverter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 物流方式查询实现
 *
 * <AUTHOR>
 * @since 2025/8/28
 */
@Repository
@RequiredArgsConstructor
public class LogisticsModeLookupImpl extends ServiceImpl<LogisticsModeMapper, LogisticsModePO> implements LogisticsModeLookup {

    private final LogisticsModeConverter logisticsModeConverter;

    @Override
    public Optional<LogisticsMode> findById(LogisticsModeId logisticsModeId) {
        return Optional.ofNullable(logisticsModeId)
                .map(LogisticsModeId::id)
                .flatMap(this::getOptById)
                .map(logisticsModeConverter::toDomain);
    }

    @Override
    public Optional<LogisticsMode> findByCode(String code) {
        return this.lambdaQuery()
                .eq(LogisticsModePO::getCode, code)
                .eq(LogisticsModePO::getIsDeleted, 0)
                .oneOpt()
                .map(logisticsModeConverter::toDomain);
    }

    @Override
    public List<LogisticsMode> findByCodes(Collection<String> code) {
        Set<String> codes = CollectionUtils.emptyIfNull(code).stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(codes)) {
            return List.of();
        }

        List<LogisticsModePO> pos = lambdaQuery().in(LogisticsModePO::getCode, codes).list();
        return logisticsModeConverter.toDomains(pos);
    }

    @Override
    public List<LogisticsMode> findByType(String type) {
        if (StringUtils.isBlank(type)) {
            return List.of();
        }
        List<LogisticsModePO> pos = lambdaQuery().eq(LogisticsModePO::getType, type).list();
        return logisticsModeConverter.toDomains(pos);
    }

    @Override
    public List<LogisticsMode> findByWarehouseIds(Collection<WarehouseId> warehouseIds) {
        Set<Integer> wids = CollectionUtils.emptyIfNull(warehouseIds).stream().filter(Objects::nonNull).map(WarehouseId::id).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(wids)) {
            return List.of();
        }

        List<LogisticsModePO> pos = lambdaQuery().in(LogisticsModePO::getAvailableWarehouseId, wids).list();
        return logisticsModeConverter.toDomains(pos);
    }

}
