package com.renpho.erp.tms.infrastructure.persistence.logisticsmode.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.tms.domain.logisticsmode.LogisticsMode;
import com.renpho.erp.tms.domain.logisticsmode.LogisticsModeId;
import com.renpho.erp.tms.domain.logisticsmode.LogisticsModeLookup;
import com.renpho.erp.tms.domain.warehouse.WarehouseId;
import com.renpho.erp.tms.infrastructure.persistence.logisticsmode.mapper.LogisticsModeMapper;
import com.renpho.erp.tms.infrastructure.persistence.logisticsmode.po.LogisticsModePO;
import com.renpho.erp.tms.infrastructure.persistence.logisticsmode.po.converter.LogisticsModeConverter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 物流方式查询实现
 *
 * <AUTHOR>
 * @since 2025/8/28
 */
@Repository
@RequiredArgsConstructor
public class LogisticsModeLookupImpl extends ServiceImpl<LogisticsModeMapper, LogisticsModePO> implements LogisticsModeLookup {

    private final LogisticsModeConverter logisticsModeConverter;

    @Override
    public Optional<LogisticsMode> findById(LogisticsModeId logisticsModeId) {
        return Optional.ofNullable(logisticsModeId)
                .map(LogisticsModeId::id)
                .flatMap(this::getOptById)
                .map(logisticsModeConverter::toDomain);
    }

    @Override
    public Optional<LogisticsMode> findByCode(String code) {
        return this.lambdaQuery()
                .eq(LogisticsModePO::getCode, code)
                .eq(LogisticsModePO::getIsDeleted, 0)
                .oneOpt()
                .map(logisticsModeConverter::toDomain);
    }

    @Override
    public List<LogisticsMode> findByType(String type) {
        if (StringUtils.isBlank(type)) {
            return List.of();
        }
        return lambdaQuery()
                .eq(LogisticsModePO::getType, type)
                .list()
                .stream()
                .map(logisticsModeConverter::toDomain)
                .toList();
    }

    @Override
    public List<LogisticsMode> findByWarehouseIds(Collection<WarehouseId> warehouseIds) {
        return List.of();
    }

}
