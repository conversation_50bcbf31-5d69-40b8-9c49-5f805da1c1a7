package com.renpho.erp.tms.infrastructure.persistence.logisticsmode.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 * 物流方式字典表 PO
 *
 * <AUTHOR>
 * @since 2025/8/28
 */
@Getter
@Setter
@TableName(value = "tms_logistics_mode", autoResultMap = true)
public class LogisticsModePO extends DefaultPO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 物流服务代码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 服务商的物流方式名称中文
     */
    @TableField(value = "name_cn")
    private String nameCn;

    /**
     * 服务商的物流方式名称英文
     */
    @TableField(value = "name_en")
    private String nameEn;

    /**
     * 订单类型
     */
    @TableField(value = "type")
    private String type;

    /**
     * 可用仓库id（做过滤）
     */
    @TableField(value = "available_warehouse_id")
    private Integer availableWarehouseId;

    /**
     * 可用仓库code（做过滤）
     */
    @TableField(value = "available_warehouse_code")
    private String availableWarehouseCode;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 状态, 暂未使用
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 是否删除, 0-未删除, 1-删除
     */
    @TableField(value = "is_deleted")
    private Integer isDeleted;
}
